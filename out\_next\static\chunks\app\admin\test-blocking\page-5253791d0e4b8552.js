(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6393],{12:(e,s,t)=>{"use strict";t.d(s,{M4:()=>o,_f:()=>l});var i=t(6104),a=t(4752),n=t.n(a);function r(e){try{Object.keys(localStorage).forEach(s=>{(s.includes(e)||s.startsWith("video_session_")||s.startsWith("watch_times_")||s.startsWith("video_refresh_")||s.startsWith("video_change_notification_")||s.startsWith("leave_")||s.includes("mytube_")||s.includes("user_"))&&localStorage.removeItem(s)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&r(e),await i.j2.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=s}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&r(e),await i.j2.signOut(),window.location.href=s}catch(e){console.error("Quick logout error:",e),window.location.href=s}}},2719:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var i=t(5155),a=t(2115),n=t(6874),r=t.n(n),l=t(6681),o=t(3592),c=t(4752),d=t.n(c);function m(){let{user:e,loading:s,isAdmin:t}=(0,l.wC)(),[n,c]=(0,a.useState)(!1),m=async()=>{try{c(!0),await (0,o.z8)({title:"\uD83D\uDEA8 Important System Update",message:"This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.",type:"warning",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users. Users will need to acknowledge this before accessing any features.",timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error sending blocking notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send blocking notification. Please try again."})}finally{c(!1)}},u=async()=>{try{c(!0),await (0,o.z8)({title:"Another Test Notification",message:"This is another test notification. All notifications are now blocking and users must acknowledge them.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users.",timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error sending notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{c(!1)}};return s?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,i.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,i.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,i.jsx)("div",{className:"flex items-center justify-between px-6 py-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(r(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,i.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Blocking Notifications"})]})})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,i.jsxs)("h2",{className:"text-lg font-bold text-blue-900 mb-3",children:[(0,i.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Test Notifications"]}),(0,i.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"1."})," Send a notification using the buttons below"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"2."})," Open a new tab and go to the user dashboard or work page"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"3."})," You should see a full-page modal that blocks all activities until acknowledged"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"4."}),' Click "Acknowledge" to dismiss the notification']}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"5."})," All notifications are now blocking/mandatory for better user engagement"]})]})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 text-2xl"})}),(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Warning Notification"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends a warning notification that users must acknowledge before continuing"}),(0,i.jsx)("button",{onClick:m,disabled:n,className:"w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Warning Notification"]})})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("i",{className:"fas fa-bell text-blue-600 text-2xl"})}),(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDCE2 Info Notification"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends an info notification that users must acknowledge before continuing"}),(0,i.jsx)("button",{onClick:u,disabled:n,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:n?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Info Notification"]})})]})})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-6",children:[(0,i.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:[(0,i.jsx)("i",{className:"fas fa-check-circle mr-2 text-green-500"}),"Notification Features (All Blocking)"]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-shield-alt text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Blocks all user activities until acknowledged"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-eye text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Forces users to read important announcements"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-users text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Can target all users or specific users"})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-mobile-alt text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Works on all pages (dashboard, work, wallet, etc.)"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-chart-line text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Progress indicator for multiple notifications"})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-clock text-green-500 mr-3"}),(0,i.jsx)("span",{children:"Persistent until user acknowledges"})]})]})]})]})]})})]})}},4552:(e,s,t)=>{Promise.resolve().then(t.bind(t,2719))},6104:(e,s,t)=>{"use strict";t.d(s,{db:()=>c,j2:()=>o});var i=t(3915),a=t(3004),n=t(5317),r=t(858);let l=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,a.xI)(l),c=(0,n.aU)(l);(0,r.c7)(l)},6681:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>o,hD:()=>l,wC:()=>c});var i=t(2115),a=t(3004),n=t(6104),r=t(12);function l(){let[e,s]=(0,i.useState)(null),[t,l]=(0,i.useState)(!0);(0,i.useEffect)(()=>{try{let e=(0,a.hg)(n.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),s(e),l(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),l(!1)}},[]);let o=async()=>{try{await (0,r.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:o}}function o(){let{user:e,loading:s}=l();return(0,i.useEffect)(()=>{s||e||(window.location.href="/login")},[e,s]),{user:e,loading:s}}function c(){let{user:e,loading:s}=l(),[t,a]=(0,i.useState)(!1),[n,r]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{if(!s&&!e){window.location.href="/admin/login";return}if(e){let s=["<EMAIL>","<EMAIL>"].includes(e.email||"");a(s),r(!1),s||(window.location.href="/login")}},[e,s]),{user:e,loading:s||n,isAdmin:t}}}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>s(4552)),_N_E=e.O()}]);