(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{12:(e,t,a)=>{"use strict";a.d(t,{G9:()=>d,M4:()=>u,_f:()=>l,g4:()=>n});var r=a(6104),o=a(4752),s=a.n(o);function n(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),o="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem("backup_timestamp_".concat(e));if(!s)return!1;if(new Date(parseInt(s)).toDateString()!==t)return i(e),!1;let n=localStorage.getItem("backup_".concat(a)),c=localStorage.getItem("backup_".concat(r)),l=localStorage.getItem("backup_".concat(o)),u=!1;if(n&&(localStorage.setItem(a,n),u=!0),c&&(localStorage.setItem(r,c),u=!0),l&&(localStorage.setItem(o,l),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),o="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem(a),n=localStorage.getItem(r),i=localStorage.getItem(o);s&&localStorage.setItem("backup_".concat(a),s),n&&localStorage.setItem("backup_".concat(r),n),i&&localStorage.setItem("backup_".concat(o),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:s,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await r.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,a),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return c},getImageProps:function(){return i}});let r=a(8229),o=a(8883),s=a(3063),n=r._(a(1193));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let c=s.Image},2590:(e,t,a)=>{Promise.resolve().then(a.bind(a,9690))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>c});var r=a(3915),o=a(3004),s=a(5317),n=a(858);let i=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,o.xI)(i),l=(0,s.aU)(i);(0,n.c7)(i)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>c,hD:()=>i,wC:()=>l});var r=a(2115),o=a(3004),s=a(6104),n=a(12);function i(){let[e,t]=(0,r.useState)(null),[a,i]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,o.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:c}}function c(){let{user:e,loading:t}=i();return(0,r.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=i(),[a,o]=(0,r.useState)(!1),[s,n]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");o(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:a}}},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>o.a});var r=a(1469),o=a.n(r)},9690:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(5155),o=a(2115),s=a(6874),n=a.n(s),i=a(6766),c=a(3004),l=a(6104),u=a(6681),d=a(4752),m=a.n(d);function g(){let{user:e,loading:t}=(0,u.hD)(),[a,s]=(0,o.useState)(""),[d,g]=(0,o.useState)(""),[h,f]=(0,o.useState)(!1),[p,w]=(0,o.useState)(!1),[b,x]=(0,o.useState)(!1);(0,o.useEffect)(()=>{if("true"===new URLSearchParams(window.location.search).get("restore")&&(x(!0),setTimeout(()=>{m().fire({icon:"info",title:"Session Expired",text:"Your session expired. Please log in again to restore your progress.",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)",timer:5e3,showConfirmButton:!0})},500)),e&&!t){let e=b?"/work":"/dashboard";window.location.href=e}},[e,t,b]);let v=async e=>{if(e.preventDefault(),!a||!d)return void m().fire({icon:"error",title:"Error",text:"Please fill in all fields",background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"});f(!0);try{await (0,c.x9)(l.j2,a,d)}catch(t){console.error("Login error:",t);let e="An error occurred during login";switch(t.code){case"auth/user-not-found":e="No account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=t.message||"Login failed"}m().fire({icon:"error",title:"Login Failed",text:e,background:"rgba(255, 255, 255, 0.95)",backdrop:"rgba(0, 0, 0, 0.8)"}),g("")}finally{f(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(i.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:b?"Session Expired":"Welcome Back"}),(0,r.jsx)("p",{className:"text-white/80",children:b?"Log in to restore your progress":"Sign in to continue earning"}),b&&(0,r.jsx)("div",{className:"mt-4 bg-orange-500/20 border border-orange-400/30 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center text-center",children:[(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-orange-400 mr-2"}),(0,r.jsx)("span",{className:"text-orange-300 text-sm",children:"Your work progress will be restored after login"})]})})]}),(0,r.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,r.jsx)("input",{type:"email",id:"email",value:a,onChange:e=>s(e.target.value),className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:p?"text":"password",id:"password",value:d,onChange:e=>g(e.target.value),className:"form-input pr-12",placeholder:"Enter your password",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>w(!p),className:"password-toggle-btn","aria-label":p?"Hide password":"Show password",children:(0,r.jsx)("i",{className:"fas ".concat(p?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Logging in..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Login"]})})]}),(0,r.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,r.jsx)(n(),{href:"/forgot-password",className:"text-white/80 hover:text-white transition-colors",children:"Forgot your password?"}),(0,r.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,r.jsx)(n(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)(n(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(2590)),_N_E=e.O()}]);