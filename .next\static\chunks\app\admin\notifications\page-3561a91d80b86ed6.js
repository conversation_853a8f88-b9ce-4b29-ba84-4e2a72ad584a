(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993,6779],{4880:(e,t,s)=>{Promise.resolve().then(s.bind(s,9119))},6779:(e,t,s)=>{"use strict";s.d(t,{CF:()=>d,I0:()=>h,Pn:()=>n,TK:()=>m,getWithdrawals:()=>u,hG:()=>g,lo:()=>o,nQ:()=>x,updateWithdrawalStatus:()=>f,x5:()=>c});var a=s(5317),i=s(6104),r=s(3592);let l=new Map;async function n(){let e="dashboard-stats",t=function(e){let t=l.get(e);return t&&Date.now()-t.timestamp<3e5?t.data:null}(e);if(t)return t;try{let t=new Date;t.setHours(0,0,0,0);let s=a.Dc.fromDate(t),n=await (0,a.getDocs)((0,a.collection)(i.db,r.COLLECTIONS.users)),o=n.size,c=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users),(0,a._M)(r.FIELD_NAMES.joinedDate,">=",s)),d=(await (0,a.getDocs)(c)).size,x=0,h=0,u=0,m=0;n.forEach(e=>{var s;let a=e.data();x+=a[r.FIELD_NAMES.totalVideos]||0,h+=a[r.FIELD_NAMES.wallet]||0;let i=null==(s=a[r.FIELD_NAMES.lastVideoDate])?void 0:s.toDate();i&&i.toDateString()===t.toDateString()&&(u+=a[r.FIELD_NAMES.todayVideos]||0)});try{let e=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.transactions),(0,a._M)(r.FIELD_NAMES.type,"==","video_earning"),(0,a.AB)(1e3));(await (0,a.getDocs)(e)).forEach(e=>{var s;let a=e.data(),i=null==(s=a[r.FIELD_NAMES.date])?void 0:s.toDate();i&&i>=t&&(m+=a[r.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let g=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.withdrawals),(0,a._M)("status","==","pending")),f=(await (0,a.getDocs)(g)).size,y=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.withdrawals),(0,a._M)("date",">=",s)),p=(await (0,a.getDocs)(y)).size,b={totalUsers:o,totalVideos:x,totalEarnings:h,pendingWithdrawals:f,todayUsers:d,todayVideos:u,todayEarnings:m,todayWithdrawals:p};return l.set(e,{data:b,timestamp:Date.now()}),b}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users),(0,a.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users),(0,a.My)(r.FIELD_NAMES.joinedDate,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let l=await (0,a.getDocs)(s);return{users:l.docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function c(e){try{if(!e||0===e.trim().length)return[];let t=e.toLowerCase().trim(),s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users),(0,a.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(s)).docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}}).filter(e=>{let s=String(e[r.FIELD_NAMES.name]||"").toLowerCase(),a=String(e[r.FIELD_NAMES.email]||"").toLowerCase(),i=String(e[r.FIELD_NAMES.mobile]||"").toLowerCase(),l=String(e[r.FIELD_NAMES.referralCode]||"").toLowerCase();return s.includes(t)||a.includes(t)||i.includes(t)||l.includes(t)})}catch(e){throw console.error("Error searching users:",e),e}}async function d(){try{let e=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users),(0,a.My)(r.FIELD_NAMES.joinedDate,"desc"));return(await (0,a.getDocs)(e)).docs.map(e=>{var t,s;return{id:e.id,...e.data(),joinedDate:null==(t=e.data()[r.FIELD_NAMES.joinedDate])?void 0:t.toDate(),planExpiry:null==(s=e.data()[r.FIELD_NAMES.planExpiry])?void 0:s.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function x(){try{let e=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.users));return(await (0,a.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.transactions),(0,a.My)(r.FIELD_NAMES.date,"desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.transactions),(0,a.My)(r.FIELD_NAMES.date,"desc"),(0,a.HM)(t),(0,a.AB)(e)));let l=await (0,a.getDocs)(s);return{transactions:l.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data()[r.FIELD_NAMES.date])?void 0:t.toDate()}}),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.AB)(e));t&&(s=(0,a.P)((0,a.collection)(i.db,r.COLLECTIONS.withdrawals),(0,a.My)("date","desc"),(0,a.HM)(t),(0,a.AB)(e)));let l=await (0,a.getDocs)(s);return{withdrawals:l.docs.map(e=>{var t;return{id:e.id,...e.data(),date:null==(t=e.data().date)?void 0:t.toDate()}}),lastDoc:l.docs[l.docs.length-1]||null,hasMore:l.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function m(e,t){try{await (0,a.mZ)((0,a.H9)(i.db,r.COLLECTIONS.users,e),t),l.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function g(e){try{await (0,a.kd)((0,a.H9)(i.db,r.COLLECTIONS.users,e)),l.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function f(e,t,n){try{let o=await (0,a.x7)((0,a.H9)(i.db,r.COLLECTIONS.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:c,amount:d,status:x}=o.data(),h={status:t,updatedAt:a.Dc.now()};if(n&&(h.adminNotes=n),await (0,a.mZ)((0,a.H9)(i.db,r.COLLECTIONS.withdrawals,e),h),"approved"===t&&"approved"!==x){let{addTransaction:e}=await Promise.resolve().then(s.bind(s,3592));await e(c,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(d," processed for transfer")})}if("rejected"===t&&"rejected"!==x){let{updateWalletBalance:e,addTransaction:t}=await Promise.resolve().then(s.bind(s,3592));await e(c,d),await t(c,{type:"withdrawal_rejected",amount:d,description:"Withdrawal rejected - ₹".concat(d," credited back to wallet")})}l.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},9119:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(5155),i=s(2115),r=s(6874),l=s.n(r),n=s(6681),o=s(3592),c=s(6779),d=s(3737),x=s(4752),h=s.n(x);function u(){let{user:e,loading:t,isAdmin:s}=(0,n.wC)(),[r,x]=(0,i.useState)([]),[u,m]=(0,i.useState)([]),[g,f]=(0,i.useState)(!0),[y,p]=(0,i.useState)(!1),[b,N]=(0,i.useState)(!1),[w,j]=(0,i.useState)([]),[v,E]=(0,i.useState)(!1),[D,C]=(0,i.useState)({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]});(0,i.useEffect)(()=>{s&&S()},[s]);let S=async()=>{try{f(!0);let[e,t]=await Promise.all([(0,o._f)(50),(0,c.lo)()]);x(e),m(t.users)}catch(e){console.error("Error loading data:",e),h().fire({icon:"error",title:"Error",text:"Failed to load data. Please try again."})}finally{f(!1)}},L=async()=>{try{N(!0),await (0,o.z8)({title:"Test Notification",message:"This is a test notification to verify the system is working correctly.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),h().fire({icon:"success",title:"Test Notification Sent!",text:"Test notification sent to all users. Check user dashboards to verify delivery.",timer:3e3,showConfirmButton:!1}),S()}catch(e){console.error("Error sending test notification:",e),h().fire({icon:"error",title:"Test Failed",text:"Failed to send test notification. Please try again."})}finally{N(!1)}},I=async()=>{try{if(!D.title.trim()||!D.message.trim())return void h().fire({icon:"error",title:"Validation Error",text:"Please fill in both title and message."});if("specific"===D.targetUsers&&0===D.selectedUserIds.length)return void h().fire({icon:"error",title:"Validation Error",text:"Please select at least one user for specific targeting."});N(!0),console.log("Sending notification:",{title:D.title.trim(),message:D.message.trim(),type:D.type,targetUsers:D.targetUsers,userIds:"specific"===D.targetUsers?D.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),await (0,o.z8)({title:D.title.trim(),message:D.message.trim(),type:D.type,targetUsers:D.targetUsers,userIds:"specific"===D.targetUsers?D.selectedUserIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),h().fire({icon:"success",title:"Notification Sent!",text:"Notification sent to ".concat("all"===D.targetUsers?"all users":"".concat(D.selectedUserIds.length," selected users"),"."),timer:3e3,showConfirmButton:!1}),C({title:"",message:"",type:"info",targetUsers:"all",selectedUserIds:[]}),p(!1),S()}catch(e){console.error("Error sending notification:",e),h().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{N(!1)}},M=e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}},A=e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"Just now";if(t<3600){let e=Math.floor(t/60);return"".concat(e," minute").concat(e>1?"s":""," ago")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e," hour").concat(e>1?"s":""," ago")}{let e=Math.floor(t/86400);return"".concat(e," day").concat(e>1?"s":""," ago")}},k=async(e,t)=>{if((await h().fire({icon:"warning",title:"Delete Notification",text:'Are you sure you want to delete "'.concat(t,'"? This action cannot be undone.'),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await (0,o.fP)(e),x(t=>t.filter(t=>t.id!==e)),h().fire({icon:"success",title:"Notification Deleted",text:"Notification has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notification:",e),h().fire({icon:"error",title:"Delete Failed",text:"Failed to delete notification. Please try again."})}finally{E(!1)}},F=async()=>{if(0===w.length)return void h().fire({icon:"warning",title:"No Selection",text:"Please select notifications to delete."});if((await h().fire({icon:"warning",title:"Delete Selected Notifications",text:"Are you sure you want to delete ".concat(w.length," selected notifications? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete All",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{E(!0),await Promise.all(w.map(e=>(0,o.fP)(e))),x(e=>e.filter(e=>!w.includes(e.id))),j([]),h().fire({icon:"success",title:"Notifications Deleted",text:"".concat(w.length," notifications have been deleted successfully"),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting notifications:",e),h().fire({icon:"error",title:"Delete Failed",text:"Failed to delete some notifications. Please try again."})}finally{E(!1)}},O=e=>{j(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return t||g?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(l(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-gray-700",children:["Total: ",r.length,w.length>0&&(0,a.jsxs)("span",{className:"ml-2 text-blue-600",children:["(",w.length," selected)"]})]}),w.length>0&&(0,a.jsxs)("button",{onClick:F,disabled:v,className:"bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-trash mr-2"}),"Delete Selected (",w.length,")"]}),(0,a.jsxs)("button",{onClick:L,disabled:b||v,className:"bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-vial mr-2"}),"Test Notification"]}),(0,a.jsxs)("button",{onClick:()=>{if(0===r.length)return void h().fire({icon:"warning",title:"No Data",text:"No notifications to export."});let e=(0,d.Pe)(r);(0,d.Bf)(e,"notifications"),h().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(r.length," notifications to CSV file."),timer:2e3,showConfirmButton:!1})},disabled:v,className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,a.jsxs)("button",{onClick:()=>p(!0),disabled:v,className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Send Notification"]}),(0,a.jsxs)("button",{onClick:S,disabled:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===r.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"fas fa-bell-slash text-gray-300 text-6xl mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notifications sent yet"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Start by sending your first notification to users"}),(0,a.jsxs)("button",{onClick:()=>p(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Send First Notification"]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:w.length===r.length&&r.length>0,onChange:()=>{w.length===r.length?j([]):j(r.map(e=>e.id).filter(Boolean))},className:"mr-3"}),(0,a.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Select All (",r.length," notifications)"]})]}),w.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[w.length," selected"]}),(0,a.jsxs)("button",{onClick:F,disabled:v,className:"text-red-600 hover:text-red-800 disabled:opacity-50",children:[(0,a.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete Selected"]})]})]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:r.map(e=>{var t;return(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 ".concat(w.includes(e.id)?"bg-blue-50":""),children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("input",{type:"checkbox",checked:w.includes(e.id),onChange:()=>O(e.id),className:"mr-3"})}),(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,a.jsx)("i",{className:M(e.type)})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[e.title,(0,a.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-bold bg-red-100 text-red-800 rounded-full",children:"\uD83D\uDEA8 BLOCKING"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("success"===e.type?"bg-green-100 text-green-800":"warning"===e.type?"bg-yellow-100 text-yellow-800":"error"===e.type?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:e.type.charAt(0).toUpperCase()+e.type.slice(1)}),(0,a.jsx)("button",{onClick:()=>k(e.id,e.title),disabled:v,className:"text-red-600 hover:text-red-800 disabled:opacity-50 p-1",title:"Delete notification",children:(0,a.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,a.jsx)("p",{className:"text-gray-700 mt-2",children:e.message}),(0,a.jsx)("div",{className:"flex items-center justify-between mt-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-user mr-1"}),"By: ",e.createdBy]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-users mr-1"}),"Target: ","all"===e.targetUsers?"All Users":"".concat((null==(t=e.userIds)?void 0:t.length)||0," Selected Users")]}),(0,a.jsxs)("span",{children:[(0,a.jsx)("i",{className:"fas fa-clock mr-1"}),A(e.createdAt)]})]})})]})]})},e.id)})})]})})}),y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Send Notification"}),(0,a.jsx)("button",{onClick:()=>p(!1),className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),(0,a.jsx)("input",{type:"text",value:D.title,onChange:e=>C(t=>({...t,title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification title..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Message"}),(0,a.jsx)("textarea",{value:D.message,onChange:e=>C(t=>({...t,message:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter notification message..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,a.jsxs)("select",{value:D.type,onChange:e=>C(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"info",children:"Info"}),(0,a.jsx)("option",{value:"success",children:"Success"}),(0,a.jsx)("option",{value:"warning",children:"Warning"}),(0,a.jsx)("option",{value:"error",children:"Error"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Target"}),(0,a.jsxs)("select",{value:D.targetUsers,onChange:e=>C(t=>({...t,targetUsers:e.target.value,selectedUserIds:"all"===e.target.value?[]:t.selectedUserIds})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Users"}),(0,a.jsx)("option",{value:"specific",children:"Specific Users"})]})]})]}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-red-500 mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"\uD83D\uDEA8 All Notifications are Blocking (Mandatory)"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Users must acknowledge this notification before they can continue with any activities (watching videos, accessing dashboard features, etc.)"})]})]})}),"specific"===D.targetUsers&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Users"}),(0,a.jsx)("div",{className:"max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-2",children:u.map(e=>(0,a.jsxs)("label",{className:"flex items-center p-2 hover:bg-gray-50 rounded",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.selectedUserIds.includes(e.id),onChange:t=>{t.target.checked?C(t=>({...t,selectedUserIds:[...t.selectedUserIds,e.id]})):C(t=>({...t,selectedUserIds:t.selectedUserIds.filter(t=>t!==e.id)}))},className:"mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.name||"Unknown User"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.email||"No email"," • ",e.plan||"No plan"]})]})]},e.id))}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[D.selectedUserIds.length," user(s) selected"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,a.jsx)("button",{onClick:()=>p(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,a.jsx)("button",{onClick:I,disabled:b,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Notification"]})})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,6465,8441,1684,7358],()=>t(4880)),_N_E=e.O()}]);