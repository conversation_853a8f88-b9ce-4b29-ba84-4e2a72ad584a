(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[481],{12:(e,t,a)=>{"use strict";a.d(t,{G9:()=>u,M4:()=>d,_f:()=>l,g4:()=>s});var o=a(6104),r=a(4752),i=a.n(r);function s(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),i=localStorage.getItem("backup_timestamp_".concat(e));if(!i)return!1;if(new Date(parseInt(i)).toDateString()!==t)return n(e),!1;let s=localStorage.getItem("backup_".concat(a)),c=localStorage.getItem("backup_".concat(o)),l=localStorage.getItem("backup_".concat(r)),d=!1;if(s&&(localStorage.setItem(a,s),d=!0),c&&(localStorage.setItem(o,c),d=!0),l&&(localStorage.setItem(r,l),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:s,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),n(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function n(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),i=localStorage.getItem(a),s=localStorage.getItem(o),n=localStorage.getItem(r);i&&localStorage.setItem("backup_".concat(a),i),s&&localStorage.setItem("backup_".concat(o),s),n&&localStorage.setItem("backup_".concat(r),n),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:i,watchTimesCount:s?JSON.parse(s).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await o.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,a),await o.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},4871:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var o=a(5155),r=a(2115),i=a(6874),s=a.n(i),n=a(6681),c=a(4752),l=a.n(c);let d=[{id:"trial",name:"Trial",price:0,duration:2,earningPerVideo:10,videoDuration:30,features:["2 days access","₹10 per batch (50 videos)","Basic support","Video duration: 30 seconds"]},{id:"starter",name:"Starter",price:499,duration:30,earningPerVideo:25,videoDuration:300,features:["30 days access","₹25 per batch (50 videos)","Priority support","Referral bonus: ₹50","Video duration: 5 minutes"]},{id:"basic",name:"Basic",price:1499,duration:30,earningPerVideo:75,videoDuration:300,features:["30 days access","₹75 per batch (50 videos)","Priority support","Referral bonus: ₹150","50 videos credited to total count","Video duration: 5 minutes"],popular:!0},{id:"premium",name:"Premium",price:2999,duration:30,earningPerVideo:150,videoDuration:300,features:["30 days access","₹150 per batch (50 videos)","Premium support","Referral bonus: ₹300","50 videos credited to total count","Video duration: 5 minutes"]},{id:"gold",name:"Gold",price:3999,duration:30,earningPerVideo:200,videoDuration:180,features:["30 days access","₹200 per batch (50 videos)","Premium support","Referral bonus: ₹400","50 videos credited to total count","Video duration: 3 minutes","Priority customer support"]},{id:"platinum",name:"Platinum",price:5999,duration:30,earningPerVideo:250,videoDuration:120,features:["30 days access","₹250 per batch (50 videos)","Premium support","Referral bonus: ₹700","50 videos credited to total count","Video duration: 2 minutes","Dedicated account manager","Early access to new features"]},{id:"diamond",name:"Diamond",price:9999,duration:30,earningPerVideo:400,videoDuration:60,features:["30 days access","₹400 per batch (50 videos)","VIP support","Referral bonus: ₹1200","50 videos credited to total count","Video duration: 1 minute","Dedicated account manager","Early access to new features","Exclusive earning opportunities"]}];function u(){let{user:e,loading:t}=(0,n.hD)(),[a,i]=(0,r.useState)(null),[c,u]=(0,r.useState)(!1),m=async t=>{if(!e)return void l().fire({icon:"info",title:"Login Required",text:"Please login to purchase a plan",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&(window.location.href="/login")});if("trial"===t.id)return void l().fire({icon:"info",title:"Trial Plan",text:"You are already on the trial plan. Upgrade to a paid plan for better earnings!"});i(t.id),u(!0);try{await new Promise(e=>setTimeout(e,2e3)),l().fire({icon:"info",title:"Payment Integration Required",html:"\n          <p>To complete your purchase of the <strong>".concat(t.name,"</strong> plan (₹").concat(t.price,'), please contact our support team.</p>\n          <br>\n          <p><strong>Plan Details:</strong></p>\n          <ul style="text-align: left; margin: 10px 0;">\n            <li>Duration: ').concat(t.duration," days</li>\n            <li>Earning: ₹").concat(t.earningPerVideo," per 50 videos</li>\n            <li>Video duration: ").concat(t.videoDuration<60?"".concat(t.videoDuration," seconds"):"".concat(Math.floor(t.videoDuration/60)," minute").concat(t.videoDuration>=120?"s":""),"</li>\n          </ul>\n          <br>\n          <p><strong>Contact Options:</strong></p>\n          <p>\uD83D\uDCE7 Email: <strong><EMAIL></strong></p>\n        "),confirmButtonText:"Contact Support",showCancelButton:!0,cancelButtonText:"Cancel"})}catch(e){console.error("Error processing plan selection:",e),l().fire({icon:"error",title:"Error",text:"Failed to process plan selection. Please try again."})}finally{u(!1),i(null)}};return t?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,o.jsx)("div",{className:"spinner"})}):(0,o.jsxs)("div",{className:"min-h-screen p-4",children:[(0,o.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)(s(),{href:e?"/dashboard":"/",className:"glass-button px-4 py-2 text-white",children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back"]}),(0,o.jsx)("h1",{className:"text-xl font-bold text-white",children:"Choose Your Plan"}),(0,o.jsx)("div",{className:"w-20"})," "]})}),(0,o.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,o.jsxs)("div",{className:"text-center mb-12",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Start Earning with MyTube"}),(0,o.jsx)("p",{className:"text-white/80 text-lg max-w-2xl mx-auto",children:"Choose the perfect plan for your earning goals. Watch videos and earn money with our flexible pricing options."})]}),(0,o.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6",children:d.map(e=>(0,o.jsxs)("div",{className:"glass-card p-8 relative ".concat(e.popular?"ring-2 ring-yellow-400":""),children:[e.popular&&(0,o.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,o.jsx)("span",{className:"bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold",children:"Most Popular"})}),(0,o.jsxs)("div",{className:"text-center mb-6",children:[(0,o.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsxs)("span",{className:"text-4xl font-bold text-white",children:["₹",e.price]}),e.price>0&&(0,o.jsxs)("span",{className:"text-white/60 ml-2",children:["/ ",e.duration," days"]})]}),(0,o.jsxs)("p",{className:"text-green-400 font-semibold",children:["Earn ₹",e.earningPerVideo," per 50 videos"]})]}),(0,o.jsx)("ul",{className:"space-y-3 mb-8",children:e.features.map((e,t)=>(0,o.jsxs)("li",{className:"flex items-center text-white/80",children:[(0,o.jsx)("i",{className:"fas fa-check text-green-400 mr-3"}),e]},t))}),(0,o.jsx)("button",{onClick:()=>m(e),disabled:c&&a===e.id,className:"w-full py-3 rounded-lg font-semibold transition-all duration-300 ".concat(e.popular?"bg-yellow-400 text-black hover:bg-yellow-500":0===e.price?"bg-gray-600 text-white hover:bg-gray-700":"bg-youtube-red text-white hover:bg-red-700"," disabled:opacity-50"),children:c&&a===e.id?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5 inline-block"}),"Processing..."]}):0===e.price?"Current Plan":"Choose ".concat(e.name)})]},e.id))}),(0,o.jsxs)("div",{className:"mt-12 glass-card p-8",children:[(0,o.jsxs)("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:[(0,o.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Plan Benefits Explained"]}),(0,o.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Earning Structure"}),(0,o.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,o.jsx)("li",{children:"• Watch 50 videos daily to earn the full amount"}),(0,o.jsx)("li",{children:"• Each video must be watched for the full duration"}),(0,o.jsx)("li",{children:"• Earnings are credited to your earning wallet"}),(0,o.jsx)("li",{children:"• Transfer earnings to main wallet for withdrawal"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-lg font-semibold text-white mb-3",children:"Video Duration Benefits"}),(0,o.jsxs)("ul",{className:"space-y-2 text-white/80",children:[(0,o.jsx)("li",{children:"• Higher plans have shorter video durations"}),(0,o.jsx)("li",{children:"• Complete daily targets faster with premium plans"}),(0,o.jsx)("li",{children:"• Video duration is fixed per plan"}),(0,o.jsx)("li",{children:"• All videos must be watched completely"})]})]})]})]}),(0,o.jsxs)("div",{className:"mt-8 text-center",children:[(0,o.jsx)("p",{className:"text-white/60 mb-4",children:"Need help choosing a plan? Contact us during business hours (9 AM - 6 PM, working days):"}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center text-white hover:text-blue-400 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-envelope mr-2"}),"<EMAIL>"]})})]})]})]})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>c});var o=a(3915),r=a(3004),i=a(5317),s=a(858);let n=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,r.xI)(n),l=(0,i.aU)(n);(0,s.c7)(n)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>c,hD:()=>n,wC:()=>l});var o=a(2115),r=a(3004),i=a(6104),s=a(12);function n(){let[e,t]=(0,o.useState)(null),[a,n]=(0,o.useState)(!0);(0,o.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let c=async()=>{try{await (0,s.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:c}}function c(){let{user:e,loading:t}=n();return(0,o.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=n(),[a,r]=(0,o.useState)(!1),[i,s]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),s(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:a}}},9403:(e,t,a)=>{Promise.resolve().then(a.bind(a,4871))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,8441,1684,7358],()=>t(9403)),_N_E=e.O()}]);