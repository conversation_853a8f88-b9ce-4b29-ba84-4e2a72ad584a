"use strict";exports.id=3582,exports.ids=[3582],exports.modules={3582:(e,t,a)=>{a.d(t,{AX:()=>_,COLLECTIONS:()=>i,FIELD_NAMES:()=>n,Gl:()=>q,HY:()=>w,I0:()=>g,II:()=>E,IK:()=>V,Oe:()=>m,Q6:()=>S,QD:()=>C,Ss:()=>N,_f:()=>B,addTransaction:()=>u,b6:()=>l,bA:()=>U,fP:()=>T,getPlanValidityDays:()=>A,getUserData:()=>s,getVideoCountData:()=>c,getWalletData:()=>d,gj:()=>v,i8:()=>R,iA:()=>H,iF:()=>p,isUserPlanExpired:()=>k,mm:()=>h,mv:()=>P,pl:()=>y,pu:()=>F,ul:()=>M,updateWalletBalance:()=>D,w1:()=>x,wT:()=>$,x4:()=>j,xj:()=>Z,yx:()=>f,z8:()=>I,zb:()=>b});var r=a(75535),o=a(33784);let n={name:"name",email:"email",mobile:"mobile",referralCode:"referralCode",referredBy:"referredBy",referralBonusCredited:"referralBonusCredited",plan:"plan",planExpiry:"planExpiry",activeDays:"activeDays",joinedDate:"joinedDate",wallet:"wallet",bankAccountHolderName:"bankAccountHolderName",bankAccountNumber:"bankAccountNumber",bankIfscCode:"bankIfscCode",bankName:"bankName",bankDetailsUpdated:"bankDetailsUpdated",totalVideos:"totalVideos",todayVideos:"todayVideos",lastVideoDate:"lastVideoDate",videoDuration:"videoDuration",quickVideoAdvantage:"quickVideoAdvantage",quickVideoAdvantageExpiry:"quickVideoAdvantageExpiry",quickVideoAdvantageDays:"quickVideoAdvantageDays",quickVideoAdvantageSeconds:"quickVideoAdvantageSeconds",quickVideoAdvantageGrantedBy:"quickVideoAdvantageGrantedBy",quickVideoAdvantageGrantedAt:"quickVideoAdvantageGrantedAt",manuallySetActiveDays:"manuallySetActiveDays",lastActiveDaysUpdate:"lastActiveDaysUpdate",type:"type",amount:"amount",date:"date",status:"status",description:"description",userId:"userId"},i={users:"users",transactions:"transactions",withdrawals:"withdrawals",plans:"plans",settings:"settings",notifications:"notifications",adminLeaves:"adminLeaves",userLeaves:"userLeaves"};async function s(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserData:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),a={name:String(e[n.name]||""),email:String(e[n.email]||""),mobile:String(e[n.mobile]||""),referralCode:String(e[n.referralCode]||""),referredBy:String(e[n.referredBy]||""),plan:String(e[n.plan]||"Trial"),planExpiry:e[n.planExpiry]?.toDate()||null,activeDays:Number(e[n.activeDays]||0),joinedDate:e[n.joinedDate]?.toDate()||new Date,videoDuration:Number(e[n.videoDuration]||("Trial"===e[n.plan]?30:300)),quickVideoAdvantage:!!e[n.quickVideoAdvantage],quickVideoAdvantageExpiry:e[n.quickVideoAdvantageExpiry]?.toDate()||null,quickVideoAdvantageDays:Number(e[n.quickVideoAdvantageDays]||0),quickVideoAdvantageSeconds:Number(e[n.quickVideoAdvantageSeconds]||30),quickVideoAdvantageGrantedBy:String(e[n.quickVideoAdvantageGrantedBy]||""),quickVideoAdvantageGrantedAt:e[n.quickVideoAdvantageGrantedAt]?.toDate()||null};return console.log("getUserData result:",a),a}return null}catch(e){return console.error("Error getting user data:",e),null}}async function d(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getWalletData:",e),{wallet:0};let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data(),a={wallet:Number(e[n.wallet]||0)};return console.log("getWalletData result:",a),a}return{wallet:0}}catch(e){return console.error("Error getting wallet data:",e),{wallet:0}}}async function c(e){try{let t=(0,r.H9)(o.db,i.users,e),a=await (0,r.x7)(t);if(a.exists()){let o=a.data(),i=o[n.totalVideos]||0,s=o[n.todayVideos]||0,d=o[n.lastVideoDate]?.toDate(),c=new Date;if((!d||d.toDateString()!==c.toDateString())&&s>0){console.log(`🔄 Resetting daily video count for user ${e} (was ${s})`),await (0,r.mZ)(t,{[n.todayVideos]:0}),s=0;try{await p(e)}catch(e){console.error("Error updating active days during daily reset:",e)}try{let e=localStorage.getItem("lastGlobalActiveDaysReset"),t=new Date().toDateString();e&&e===t||(console.log("\uD83C\uDF05 Triggering daily active days increment for all users..."),m().then(e=>{console.log("✅ Daily active days increment completed:",e),localStorage.setItem("lastGlobalActiveDaysReset",t)}).catch(e=>{console.error("❌ Error in daily active days increment:",e)}))}catch(e){console.error("Error checking/running daily active days increment:",e)}}return{totalVideos:i,todayVideos:s,remainingVideos:Math.max(0,50-s)}}return{totalVideos:0,todayVideos:0,remainingVideos:50}}catch(e){throw console.error("Error getting video count data:",e),e}}async function l(e,t){try{await (0,r.mZ)((0,r.H9)(o.db,i.users,e),t)}catch(e){throw console.error("Error updating user data:",e),e}}async function u(e,t){try{let a={[n.userId]:e,[n.type]:t.type,[n.amount]:t.amount,[n.description]:t.description,[n.status]:t.status||"completed",[n.date]:r.Dc.now()};await (0,r.gS)((0,r.collection)(o.db,i.transactions),a)}catch(e){throw console.error("Error adding transaction:",e),e}}async function g(e,t=10){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getTransactions:",e),[];let a=(0,r.P)((0,r.collection)(o.db,i.transactions),(0,r._M)(n.userId,"==",e),(0,r.AB)(t)),s=(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data()[n.date]?.toDate()}));return s.sort((e,t)=>{let a=e.date||new Date(0);return(t.date||new Date(0)).getTime()-a.getTime()}),s}catch(e){return console.error("Error getting transactions:",e),[]}}async function y(e){try{let t=(0,r.P)((0,r.collection)(o.db,i.users),(0,r._M)(n.referredBy,"==",e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),joinedDate:e.data()[n.joinedDate]?.toDate()}))}catch(e){throw console.error("Error getting referrals:",e),e}}async function f(e){try{let t=new Date,a=(0,r.H9)(o.db,i.users,e),s=await (0,r.x7)(a);if(s.exists()){let o=s.data(),i=o[n.lastVideoDate]?.toDate(),d=o[n.todayVideos]||0;(!i||i.toDateString()!==t.toDateString())&&d>0?(console.log(`🔄 Resetting and updating daily video count for user ${e}`),await (0,r.mZ)(a,{[n.totalVideos]:(0,r.GV)(1),[n.todayVideos]:1,[n.lastVideoDate]:r.Dc.fromDate(t)})):await (0,r.mZ)(a,{[n.totalVideos]:(0,r.GV)(1),[n.todayVideos]:(0,r.GV)(1),[n.lastVideoDate]:r.Dc.fromDate(t)})}else await (0,r.mZ)(a,{[n.totalVideos]:(0,r.GV)(1),[n.todayVideos]:(0,r.GV)(1),[n.lastVideoDate]:r.Dc.fromDate(t)})}catch(e){throw console.error("Error updating video count:",e),e}}async function w(e){try{let t=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(t,{[n.todayVideos]:0}),console.log(`✅ Reset daily video count for user ${e}`)}catch(e){throw console.error("Error resetting daily video count:",e),e}}async function p(e,t=!1){try{let d=await s(e);if(!d)return void console.error("User data not found for active days update:",e);let c=(0,r.H9)(o.db,i.users,e),l=(await (0,r.x7)(c)).data();if(l?.manuallySetActiveDays&&!t)return console.log(`⏭️ Skipping active days auto-update for user ${e} - manually set by admin`),d.activeDays;let u=0;if("Trial"===d.plan){let e=d.joinedDate||new Date,t=new Date;u=Math.floor((t.getTime()-e.getTime())/864e5)+1}else{let{calculateActiveDays:t}=await a.e(7087).then(a.bind(a,87087)),r=d.planExpiry?new Date(d.planExpiry.getTime()-24*A(d.plan)*36e5):d.joinedDate||new Date,o=await t(e,r);u=Math.max(1,o+1)}let g=d.activeDays||0;return u!==g&&(console.log(`📅 Updating active days for user ${e}: ${g} → ${u}`),await (0,r.mZ)(c,{[n.activeDays]:u})),u}catch(e){throw console.error("Error updating user active days:",e),e}}async function m(){try{console.log("\uD83C\uDF05 Starting daily active days increment...");let e=new Date,t=e.toDateString(),{isAdminLeaveDay:s}=await a.e(7087).then(a.bind(a,87087));if(await s(e))return console.log("⏸️ Skipping active days increment - Admin leave day"),{incrementedCount:0,skippedCount:0,errorCount:0,reason:"Admin leave day"};let d=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),c=0,l=0,u=0;for(let s of d.docs)try{let d=s.data(),u=s.id,g=d[n.lastActiveDaysUpdate]?.toDate();if(g&&g.toDateString()===t){l++;continue}let{isUserOnLeave:y}=await a.e(7087).then(a.bind(a,87087));if(await y(u,e)){console.log(`⏸️ Skipping active days increment for user ${u} - User leave day`),await (0,r.mZ)((0,r.H9)(o.db,i.users,u),{[n.lastActiveDaysUpdate]:r.Dc.fromDate(e)}),l++;continue}let f=d[n.activeDays]||0,w=f+1;await (0,r.mZ)((0,r.H9)(o.db,i.users,u),{[n.activeDays]:w,[n.lastActiveDaysUpdate]:r.Dc.fromDate(e)}),c++,console.log(`📅 Incremented active days for user ${u}: ${f} → ${w}`)}catch(e){console.error(`Error updating active days for user ${s.id}:`,e),u++}return console.log(`✅ Daily active days increment completed: ${c} incremented, ${l} skipped, ${u} errors`),{incrementedCount:c,skippedCount:l,errorCount:u}}catch(e){throw console.error("Error in daily active days increment:",e),e}}async function v(){try{console.log("\uD83D\uDD27 Starting to fix all users active days...");let e=await (0,r.getDocs)((0,r.collection)(o.db,i.users)),t=0,a=0;for(let r of e.docs)try{await p(r.id),t++}catch(e){console.error(`Error fixing active days for user ${r.id}:`,e),a++}return console.log(`✅ Fixed active days for ${t} users, ${a} errors`),{fixedCount:t,errorCount:a}}catch(e){throw console.error("Error fixing all users active days:",e),e}}async function D(e,t){try{let a=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(a,{[n.wallet]:(0,r.GV)(t)})}catch(e){throw console.error("Error updating wallet balance:",e),e}}async function h(e,t){try{if(!e||"string"!=typeof e)throw Error("Invalid userId provided");var a=t;let{accountHolderName:s,accountNumber:d,ifscCode:c,bankName:l}=a;if(!s||s.trim().length<2)throw Error("Account holder name must be at least 2 characters long");if(!d||!/^\d{9,18}$/.test(d.trim()))throw Error("Account number must be 9-18 digits");if(!c||!/^[A-Z]{4}0[A-Z0-9]{6}$/.test(c.trim().toUpperCase()))throw Error("Invalid IFSC code format (e.g., SBIN0001234)");if(!l||l.trim().length<2)throw Error("Bank name must be at least 2 characters long");let u=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(u,{[n.bankAccountHolderName]:t.accountHolderName.trim(),[n.bankAccountNumber]:t.accountNumber.trim(),[n.bankIfscCode]:t.ifscCode.trim().toUpperCase(),[n.bankName]:t.bankName.trim(),[n.bankDetailsUpdated]:r.Dc.now()}),console.log("Bank details saved successfully for user:",e)}catch(e){throw console.error("Error saving bank details:",e),e}}async function b(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getBankDetails:",e),null;let t=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(t.exists()){let e=t.data();if(e[n.bankAccountNumber]){let t={accountHolderName:String(e[n.bankAccountHolderName]||""),accountNumber:String(e[n.bankAccountNumber]||""),ifscCode:String(e[n.bankIfscCode]||""),bankName:String(e[n.bankName]||"")};return console.log("getBankDetails result found"),t}}return console.log("No bank details found for user"),null}catch(e){return console.error("Error getting bank details:",e),null}}function A(e){return({Trial:2,Starter:30,Basic:30,Premium:30,Gold:30,Platinum:30,Diamond:30,499:30,1499:30,2999:30,3999:30,5999:30,9999:30})[e]||2}async function k(e){try{let t=await s(e);if(!t)return{expired:!0,reason:"User data not found"};if("Trial"===t.plan){let e=t.joinedDate||new Date,a=new Date,r=Math.floor((a.getTime()-e.getTime())/864e5)+1,o=Math.max(0,2-r);return{expired:o<=0,reason:o<=0?"Trial period expired":void 0,daysLeft:o,activeDays:r}}if(t.planExpiry){let e=new Date,a=e>t.planExpiry,r=a?0:Math.ceil((t.planExpiry.getTime()-e.getTime())/864e5);return{expired:a,reason:a?"Plan subscription expired":void 0,daysLeft:r,activeDays:t.activeDays||0}}let a=A(t.plan),r=t.activeDays||0,o=Math.max(0,a-r),n=o<=0;return{expired:n,reason:n?`Plan validity period (${a} days) exceeded based on active days`:void 0,daysLeft:o,activeDays:r}}catch(e){return console.error("Error checking plan expiry:",e),{expired:!0,reason:"Error checking plan status"}}}async function E(e,t,a){try{let s=(0,r.H9)(o.db,i.users,e);if("Trial"===t)await (0,r.mZ)(s,{[n.planExpiry]:null});else{let o;if(a)o=a;else{let e=A(t),a=new Date;o=new Date(a.getTime()+24*e*36e5)}await (0,r.mZ)(s,{[n.planExpiry]:r.Dc.fromDate(o)}),console.log(`Updated plan expiry for user ${e} to ${o.toDateString()}`)}}catch(e){throw console.error("Error updating plan expiry:",e),e}}async function V(e,t,a){try{if("Trial"!==t||"Trial"===a)return void console.log("Referral bonus only applies when upgrading from Trial to paid plan");console.log(`Processing referral bonus for user ${e} upgrading from ${t} to ${a}`);let s=await (0,r.x7)((0,r.H9)(o.db,i.users,e));if(!s.exists())return void console.log("User not found");let d=s.data(),c=d[n.referredBy],l=d[n.referralBonusCredited];if(!c)return void console.log("User was not referred by anyone, skipping bonus processing");if(l)return void console.log("Referral bonus already credited for this user, skipping");console.log("Finding referrer with code:",c);let g=(0,r.P)((0,r.collection)(o.db,i.users),(0,r._M)(n.referralCode,"==",c),(0,r.AB)(1)),y=await (0,r.getDocs)(g);if(y.empty)return void console.log("Referral code not found:",c);let f=y.docs[0].id,w={Trial:0,499:50,1499:150,2999:300,3999:400,5999:700,9999:1200,Starter:50,Basic:150,Premium:300,Gold:400,Platinum:700,Diamond:1200}[a]||0;if(console.log(`Found referrer: ${f}, bonus amount: ₹${w}`),w>0){await D(f,w);let t=(0,r.H9)(o.db,i.users,f);await (0,r.mZ)(t,{[n.totalVideos]:(0,r.GV)(50)});let s=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(s,{[n.referralBonusCredited]:!0}),await u(f,{type:"referral_bonus",amount:w,description:`Referral bonus for ${a} plan upgrade + 50 bonus videos (User: ${d[n.name]})`}),console.log(`✅ Referral bonus processed: ₹${w} + 50 videos for referrer ${f}`)}else console.log("No bonus amount calculated, skipping")}catch(e){console.error("❌ Error processing referral bonus:",e)}}async function S(e){try{var t;let a=await s(e);if(!a)return{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1};let r=!!(t=a).quickVideoAdvantage&&!!t.quickVideoAdvantageExpiry&&new Date<t.quickVideoAdvantageExpiry,o=a.videoDuration;return r?o=a.quickVideoAdvantageSeconds||30:o&&"Trial"!==a.plan||(o=({Trial:30,Starter:300,Basic:300,Premium:300,Gold:180,Platinum:120,Diamond:60})[a.plan]||30),{videoDuration:o,earningPerBatch:({Trial:10,Starter:25,Basic:75,Premium:150,Gold:200,Platinum:250,Diamond:400})[a.plan]||10,plan:a.plan,hasQuickAdvantage:r,quickAdvantageExpiry:a.quickVideoAdvantageExpiry}}catch(e){return console.error("Error getting user video settings:",e),{videoDuration:30,earningPerBatch:10,plan:"Trial",hasQuickAdvantage:!1}}}async function x(e,t,a,s=30){try{if(t<=0||t>365)throw Error("Days must be between 1 and 365");if(s<1||s>420)throw Error("Seconds must be between 1 and 420 (7 minutes)");let d=new Date,c=new Date(d.getTime()+24*t*36e5),l=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(l,{[n.quickVideoAdvantage]:!0,[n.quickVideoAdvantageExpiry]:r.Dc.fromDate(c),[n.quickVideoAdvantageDays]:t,[n.quickVideoAdvantageSeconds]:s,[n.quickVideoAdvantageGrantedBy]:a,[n.quickVideoAdvantageGrantedAt]:r.Dc.fromDate(d)}),console.log(`Granted quick video advantage to user ${e} for ${t} days until ${c.toDateString()}`),await u(e,{type:"quick_advantage_granted",amount:0,description:`Quick video advantage granted for ${t} days by ${a}`}),{success:!0,expiry:c}}catch(e){throw console.error("Error granting quick video advantage:",e),e}}async function $(e,t){try{let a=(0,r.H9)(o.db,i.users,e);return await (0,r.mZ)(a,{[n.quickVideoAdvantage]:!1,[n.quickVideoAdvantageExpiry]:null,[n.quickVideoAdvantageDays]:0,[n.quickVideoAdvantageSeconds]:30,[n.quickVideoAdvantageGrantedBy]:"",[n.quickVideoAdvantageGrantedAt]:null}),console.log(`Removed quick video advantage from user ${e}`),await u(e,{type:"quick_advantage_removed",amount:0,description:`Quick video advantage removed by ${t}`}),{success:!0}}catch(e){throw console.error("Error removing quick video advantage:",e),e}}async function q(e,t){try{let a=[1,10,30].includes(t),s=t>=60&&t<=420;if(!a&&!s)throw Error("Video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");let d=(0,r.H9)(o.db,i.users,e);await (0,r.mZ)(d,{[n.videoDuration]:t}),console.log(`Updated video duration for user ${e} to ${t} seconds`)}catch(e){throw console.error("Error updating user video duration:",e),e}}async function I(e){try{let t={title:e.title,message:e.message,type:e.type,targetUsers:e.targetUsers,userIds:e.userIds||[],createdAt:r.Dc.now(),createdBy:e.createdBy};console.log("Adding notification to Firestore:",t);let a=await (0,r.gS)((0,r.collection)(o.db,i.notifications),t);console.log("Notification added successfully with ID:",a.id);let n=await (0,r.x7)(a);return n.exists()?console.log("Notification verified in database:",n.data()):console.warn("Notification not found after adding"),a.id}catch(e){throw console.error("Error adding notification:",e),e}}async function N(e,t=20){try{let a,n;if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUserNotifications:",e),[];console.log(`Loading notifications for user: ${e}`);try{let e=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.My)("createdAt","desc"),(0,r.AB)(t));a=await (0,r.getDocs)(e),console.log(`Found ${a.docs.length} notifications for all users`)}catch(n){console.warn("Error querying all users notifications, trying without orderBy:",n);let e=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","all"),(0,r.AB)(t));a=await (0,r.getDocs)(e)}try{let a=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.My)("createdAt","desc"),(0,r.AB)(t));n=await (0,r.getDocs)(a),console.log(`Found ${n.docs.length} notifications for specific user`)}catch(s){console.warn("Error querying specific user notifications, trying without orderBy:",s);let a=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r._M)("targetUsers","==","specific"),(0,r._M)("userIds","array-contains",e),(0,r.AB)(t));n=await (0,r.getDocs)(a)}let s=[];a.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),n.docs.forEach(e=>{s.push({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date})}),s.sort((e,t)=>t.createdAt.getTime()-e.createdAt.getTime());let d=s.slice(0,t);return console.log(`Returning ${d.length} total notifications for user`),d}catch(e){return console.error("Error getting user notifications:",e),[]}}async function B(e=50){try{let t=(0,r.P)((0,r.collection)(o.db,i.notifications),(0,r.My)("createdAt","desc"),(0,r.AB)(e));return(await (0,r.getDocs)(t)).docs.map(e=>({id:e.id,...e.data(),createdAt:e.data().createdAt?.toDate()||new Date}))}catch(e){return console.error("Error getting all notifications:",e),[]}}async function T(e){try{if(!e||"string"!=typeof e)throw Error("Invalid notification ID provided");console.log("Deleting notification:",e),await (0,r.kd)((0,r.H9)(o.db,i.notifications,e)),console.log("Notification deleted successfully")}catch(e){throw console.error("Error deleting notification:",e),e}}async function U(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");a.includes(e)||(a.push(e),localStorage.setItem(`read_notifications_${t}`,JSON.stringify(a)))}catch(e){console.error("Error marking notification as read:",e)}}function P(e,t){try{return JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]").includes(e)}catch(e){return console.error("Error checking notification read status:",e),!1}}function M(e,t){try{let a=JSON.parse(localStorage.getItem(`read_notifications_${t}`)||"[]");return e.filter(e=>!a.includes(e.id)).length}catch(e){return console.error("Error getting unread notification count:",e),0}}async function _(e){try{if(!e||"string"!=typeof e)return console.error("Invalid userId provided to getUnreadNotifications:",e),[];console.log(`Loading unread notifications for user: ${e}`);let t=await N(e,50),a=JSON.parse(localStorage.getItem(`read_notifications_${e}`)||"[]"),r=t.filter(e=>e.id&&!a.includes(e.id));return console.log(`Found ${r.length} unread notifications`),r}catch(e){return console.error("Error getting unread notifications:",e),[]}}async function H(e){try{return(await _(e)).length>0}catch(e){return console.error("Error checking for unread notifications:",e),!1}}async function G(e){try{let t=(0,r.P)((0,r.collection)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r._M)("status","==","pending"),(0,r.AB)(1));return!(await (0,r.getDocs)(t)).empty}catch(e){return console.error("Error checking pending withdrawals:",e),!1}}async function C(e){try{let t=await s(e);if(!t)return{allowed:!1,reason:"Unable to verify user information. Please try again."};if("Trial"===t.plan)return{allowed:!1,reason:"Trial plan users cannot make withdrawals. Please upgrade to a paid plan to enable withdrawals."};if(await G(e))return{allowed:!1,reason:"You have a pending withdrawal request. Please wait for it to be processed before submitting a new request."};let r=new Date,o=r.getHours();if(o<10||o>=18)return{allowed:!1,reason:"Withdrawals are only allowed between 10:00 AM to 6:00 PM"};let{isAdminLeaveDay:n}=await a.e(7087).then(a.bind(a,87087));if(await n(r))return{allowed:!1,reason:"Withdrawals are not allowed on admin leave/holiday days"};let{isUserOnLeave:i}=await a.e(7087).then(a.bind(a,87087));if(await i(e,r))return{allowed:!1,reason:"Withdrawals are not allowed on your leave days"};return{allowed:!0}}catch(e){return console.error("Error checking withdrawal allowed:",e),{allowed:!1,reason:"Unable to verify withdrawal eligibility. Please try again."}}}async function Z(e,t,a){try{if(t<50)throw Error("Minimum withdrawal amount is ₹50");let n=await C(e);if(!n.allowed)throw Error(n.reason);if((await d(e)).wallet<t)throw Error("Insufficient wallet balance");await D(e,-t),await u(e,{type:"withdrawal_request",amount:-t,description:`Withdrawal request submitted - ₹${t} debited from wallet`});let s={userId:e,amount:t,bankDetails:a,status:"pending",date:r.Dc.now(),createdAt:r.Dc.now()};return(await (0,r.gS)((0,r.collection)(o.db,i.withdrawals),s)).id}catch(e){throw console.error("Error creating withdrawal request:",e),e}}async function R(e,t=20){try{let a=(0,r.P)((0,r.collection)(o.db,i.withdrawals),(0,r._M)("userId","==",e),(0,r.My)("date","desc"),(0,r.AB)(t));return(await (0,r.getDocs)(a)).docs.map(e=>({id:e.id,...e.data(),date:e.data().date?.toDate()}))}catch(e){return console.error("Error getting user withdrawals:",e),[]}}async function j(){try{try{let e=(0,r.collection)(o.db,i.users),t=((await (0,r.d_)(e)).data().count+1).toString().padStart(4,"0");return`MYN${t}`}catch(a){console.warn("Failed to get count from server, using fallback method:",a);let e=Date.now().toString().slice(-4),t=Math.random().toString(36).substring(2,4).toUpperCase();return`MYN${e}${t}`}}catch(t){console.error("Error generating unique referral code:",t);let e=Date.now().toString().slice(-4);return`MYN${e}`}}async function F(){return j()}}};