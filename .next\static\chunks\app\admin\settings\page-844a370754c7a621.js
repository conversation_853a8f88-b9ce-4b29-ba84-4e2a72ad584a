(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{12:(e,t,a)=>{"use strict";a.d(t,{G9:()=>u,M4:()=>d,_f:()=>c,g4:()=>o});var r=a(6104),s=a(4752),n=a.n(s);function o(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),s="daily_watch_times_".concat(e,"_").concat(t),n=localStorage.getItem("backup_timestamp_".concat(e));if(!n)return!1;if(new Date(parseInt(n)).toDateString()!==t)return i(e),!1;let o=localStorage.getItem("backup_".concat(a)),l=localStorage.getItem("backup_".concat(r)),c=localStorage.getItem("backup_".concat(s)),d=!1;if(o&&(localStorage.setItem(a,o),d=!0),l&&(localStorage.setItem(r,l),d=!0),c&&(localStorage.setItem(s,c),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:o,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function l(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),s="daily_watch_times_".concat(e,"_").concat(t),n=localStorage.getItem(a),o=localStorage.getItem(r),i=localStorage.getItem(s);n&&localStorage.setItem("backup_".concat(a),n),o&&localStorage.setItem("backup_".concat(r),o),i&&localStorage.setItem("backup_".concat(s),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:n,watchTimesCount:o?JSON.parse(o).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await n().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&l(e,!1),await r.j2.signOut(),n().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),n().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&l(e,a),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},3356:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(5155),s=a(2115),n=a(6874),o=a.n(n),i=a(6681),l=a(4752),c=a.n(l);function d(){let{user:e,loading:t,isAdmin:a}=(0,i.wC)(),[n,l]=(0,s.useState)({appName:"MyTube",appVersion:"1.0.0",maintenanceMode:!1,registrationEnabled:!0,minWithdrawalAmount:50,maxWithdrawalAmount:5e4,withdrawalProcessingTime:"24-48 hours",supportEmail:"<EMAIL>",supportPhone:"+917676636990",defaultVideoDuration:300,maxDailyVideos:50,referralBonus:50,videoEarningRates:{trial:10,starter:25,basic:75,premium:150,gold:200,platinum:250,diamond:400}}),[d,u]=(0,s.useState)(!0),[m,g]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&h()},[a]);let h=async()=>{try{u(!0),u(!1)}catch(e){console.error("Error loading settings:",e),u(!1)}},x=async()=>{try{if(g(!0),n.minWithdrawalAmount>=n.maxWithdrawalAmount)throw Error("Minimum withdrawal amount must be less than maximum withdrawal amount");let e=[1,10,30].includes(n.defaultVideoDuration),t=n.defaultVideoDuration>=60&&n.defaultVideoDuration<=420;if(!e&&!t)throw Error("Default video duration must be 1, 10, or 30 seconds for quick duration, or between 1-7 minutes (60-420 seconds) for standard duration");c().fire({icon:"success",title:"Settings Saved",text:"System settings have been updated successfully.",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error saving settings:",e),c().fire({icon:"error",title:"Save Failed",text:e.message||"Failed to save settings. Please try again."})}finally{g(!1)}},p=(e,t)=>{l(a=>({...a,[e]:t}))},b=(e,t)=>{l(a=>({...a,videoEarningRates:{...a.videoEarningRates,[e]:t}}))};return t||d?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading settings..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(o(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Settings"})]}),(0,r.jsx)("button",{onClick:x,disabled:m,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-save mr-2"}),"Save Settings"]})})]})}),(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"General Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"App Name"}),(0,r.jsx)("input",{type:"text",value:n.appName,onChange:e=>p("appName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"App Version"}),(0,r.jsx)("input",{type:"text",value:n.appVersion,onChange:e=>p("appVersion",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Support Email"}),(0,r.jsx)("input",{type:"email",value:n.supportEmail,onChange:e=>p("supportEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Support Phone (WhatsApp)",(0,r.jsx)("i",{className:"fab fa-whatsapp text-green-500 ml-1"})]}),(0,r.jsx)("input",{type:"text",value:n.supportPhone,onChange:e=>p("supportPhone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"+917676636990"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"WhatsApp number for customer support"})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"maintenanceMode",checked:n.maintenanceMode,onChange:e=>p("maintenanceMode",e.target.checked),className:"mr-2"}),(0,r.jsx)("label",{htmlFor:"maintenanceMode",className:"text-sm font-medium text-gray-700",children:"Maintenance Mode (Disable app access for users)"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"registrationEnabled",checked:n.registrationEnabled,onChange:e=>p("registrationEnabled",e.target.checked),className:"mr-2"}),(0,r.jsx)("label",{htmlFor:"registrationEnabled",className:"text-sm font-medium text-gray-700",children:"Enable New User Registration"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Video Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Default Video Duration"}),(0,r.jsxs)("select",{value:n.defaultVideoDuration,onChange:e=>p("defaultVideoDuration",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,r.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,r.jsx)("option",{value:1,children:"1 second"}),(0,r.jsx)("option",{value:10,children:"10 seconds"}),(0,r.jsx)("option",{value:30,children:"30 seconds"})]}),(0,r.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,r.jsx)("option",{value:60,children:"1 minute"}),(0,r.jsx)("option",{value:120,children:"2 minutes"}),(0,r.jsx)("option",{value:180,children:"3 minutes"}),(0,r.jsx)("option",{value:240,children:"4 minutes"}),(0,r.jsx)("option",{value:300,children:"5 minutes"}),(0,r.jsx)("option",{value:360,children:"6 minutes"}),(0,r.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:["Default duration for new users: ",n.defaultVideoDuration<60?"".concat(n.defaultVideoDuration," second").concat(n.defaultVideoDuration>1?"s":""):"".concat(Math.round(n.defaultVideoDuration/60)," minute").concat(Math.round(n.defaultVideoDuration/60)>1?"s":"")]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Daily Videos"}),(0,r.jsx)("input",{type:"number",min:"1",max:"100",value:n.maxDailyVideos,onChange:e=>p("maxDailyVideos",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Video Earning Rates (₹ per video)"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Trial Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.trial,onChange:e=>b("trial",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Starter Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.starter,onChange:e=>b("starter",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Basic Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.basic,onChange:e=>b("basic",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Premium Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.premium,onChange:e=>b("premium",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gold Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.gold,onChange:e=>b("gold",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Platinum Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.platinum,onChange:e=>b("platinum",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Diamond Plan"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.videoEarningRates.diamond,onChange:e=>b("diamond",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Withdrawal Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Minimum Withdrawal Amount (₹)"}),(0,r.jsx)("input",{type:"number",min:"1",value:n.minWithdrawalAmount,onChange:e=>p("minWithdrawalAmount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Maximum Withdrawal Amount (₹)"}),(0,r.jsx)("input",{type:"number",min:"1",value:n.maxWithdrawalAmount,onChange:e=>p("maxWithdrawalAmount",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Processing Time"}),(0,r.jsx)("input",{type:"text",value:n.withdrawalProcessingTime,onChange:e=>p("withdrawalProcessingTime",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-bold text-gray-900 mb-4",children:"Referral Settings"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Bonus (₹)"}),(0,r.jsx)("input",{type:"number",min:"0",value:n.referralBonus,onChange:e=>p("referralBonus",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Amount given to referrer when someone joins using their code"})]})})]})]})]})}},4207:(e,t,a)=>{Promise.resolve().then(a.bind(a,3356))},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>c,j2:()=>l});var r=a(3915),s=a(3004),n=a(5317),o=a(858);let i=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,s.xI)(i),c=(0,n.aU)(i);(0,o.c7)(i)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>l,hD:()=>i,wC:()=>c});var r=a(2115),s=a(3004),n=a(6104),o=a(12);function i(){let[e,t]=(0,r.useState)(null),[a,i]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,s.hg)(n.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let l=async()=>{try{await (0,o.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:l}}function l(){let{user:e,loading:t}=i();return(0,r.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=i(),[a,s]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");s(t),o(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||n,isAdmin:a}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,8441,1684,7358],()=>t(4207)),_N_E=e.O()}]);