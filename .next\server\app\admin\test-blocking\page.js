(()=>{var e={};e.id=6393,e.ids=[6393],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28605:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(60687),i=s(43210),a=s(85814),o=s.n(a),n=s(87979),l=s(3582),c=s(77567);function d(){let{user:e,loading:t,isAdmin:s}=(0,n.wC)(),[a,d]=(0,i.useState)(!1),u=async()=>{try{d(!0),await (0,l.z8)({title:"\uD83D\uDEA8 Important System Update",message:"This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.",type:"warning",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"}),c.A.fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users. Users will need to acknowledge this before accessing any features.",timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error sending blocking notification:",e),c.A.fire({icon:"error",title:"Send Failed",text:"Failed to send blocking notification. Please try again."})}finally{d(!1)}},m=async()=>{try{d(!0),await (0,l.z8)({title:"Another Test Notification",message:"This is another test notification. All notifications are now blocking and users must acknowledge them.",type:"info",targetUsers:"all",userIds:[],createdBy:e?.email||"admin"}),c.A.fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users.",timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error sending notification:",e),c.A.fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{d(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"flex items-center justify-between px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(o(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Blocking Notifications"})]})})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("h2",{className:"text-lg font-bold text-blue-900 mb-3",children:[(0,r.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Test Notifications"]}),(0,r.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"1."})," Send a notification using the buttons below"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"2."})," Open a new tab and go to the user dashboard or work page"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"3."})," You should see a full-page modal that blocks all activities until acknowledged"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"4."}),' Click "Acknowledge" to dismiss the notification']}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"5."})," All notifications are now blocking/mandatory for better user engagement"]})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Warning Notification"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends a warning notification that users must acknowledge before continuing"}),(0,r.jsx)("button",{onClick:u,disabled:a,className:"w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Warning Notification"]})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("i",{className:"fas fa-bell text-blue-600 text-2xl"})}),(0,r.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDCE2 Info Notification"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends an info notification that users must acknowledge before continuing"}),(0,r.jsx)("button",{onClick:m,disabled:a,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Info Notification"]})})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-check-circle mr-2 text-green-500"}),"Notification Features (All Blocking)"]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-shield-alt text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Blocks all user activities until acknowledged"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-eye text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Forces users to read important announcements"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-users text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Can target all users or specific users"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-mobile-alt text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Works on all pages (dashboard, work, wallet, etc.)"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-chart-line text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Progress indicator for multiple notifications"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-clock text-green-500 mr-3"}),(0,r.jsx)("span",{children:"Persistent until user acknowledges"})]})]})]})]})]})})]})}},29021:e=>{"use strict";e.exports=require("fs")},29155:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\test-blocking\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\test-blocking\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,s)=>{"use strict";s.d(t,{db:()=>c,j2:()=>l});var r=s(67989),i=s(63385),a=s(75535),o=s(70146);let n=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(n),c=(0,a.aU)(n);(0,o.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34291:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),o=s.n(a),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["test-blocking",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29155)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\test-blocking\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\test-blocking\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/test-blocking/page",pathname:"/admin/test-blocking",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},42416:(e,t,s)=>{Promise.resolve().then(s.bind(s,28605))},49040:(e,t,s)=>{Promise.resolve().then(s.bind(s,29155))},51278:(e,t,s)=>{"use strict";s.d(t,{G9:()=>d,M4:()=>c,_f:()=>l,g4:()=>a});var r=s(33784),i=s(77567);function a(e){try{let t=new Date().toDateString(),s=`video_session_${e}_${t}`,r=`watch_times_${e}_${t}`,i=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(`backup_timestamp_${e}`);if(!a)return!1;if(new Date(parseInt(a)).toDateString()!==t)return o(e),!1;let n=localStorage.getItem(`backup_${s}`),l=localStorage.getItem(`backup_${r}`),c=localStorage.getItem(`backup_${i}`),d=!1;if(n&&(localStorage.setItem(s,n),d=!0),l&&(localStorage.setItem(r,l),d=!0),c&&(localStorage.setItem(i,c),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),o(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function o(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),s=`video_session_${e}_${t}`,r=`watch_times_${e}_${t}`,i=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(s),o=localStorage.getItem(r),n=localStorage.getItem(i);a&&localStorage.setItem(`backup_${s}`,a),o&&localStorage.setItem(`backup_${r}`,o),n&&localStorage.setItem(`backup_${i}`,n),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:a,watchTimesCount:o?JSON.parse(o).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,t="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await r.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e,t="/login",s=!0){try{e&&n(e,s),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let s=localStorage.getItem(e);s&&new Date(parseInt(s)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>o,hD:()=>a,wC:()=>n});var r=s(43210);s(63385),s(33784);var i=s(51278);function a(){let[e,t]=(0,r.useState)(null),[s,a]=(0,r.useState)(!0),o=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:o}}function o(){let{user:e,loading:t}=a();return{user:e,loading:t}}function n(){let{user:e,loading:t}=a(),[s,i]=(0,r.useState)(!1),[o,n]=(0,r.useState)(!0);return{user:e,loading:t||o,isAdmin:s}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[6204,2756,7567,8441,3582],()=>s(34291));module.exports=r})();