(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9451],{1577:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var s=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(6681),c=a(3737),d=a(4752),o=a.n(d);function x(){let{user:e,loading:t,isAdmin:l}=(0,i.wC)(),[d,x]=(0,r.useState)([]),[m,h]=(0,r.useState)(!0),[g,u]=(0,r.useState)(!1),[f,y]=(0,r.useState)(!1),[p,b]=(0,r.useState)({date:"",reason:"",type:"holiday"});(0,r.useEffect)(()=>{l&&v()},[l]);let v=async()=>{try{h(!0);let{getAdminLeaves:e}=await a.e(9567).then(a.bind(a,9567)),t=await e();x(t)}catch(e){console.error("Error loading leaves:",e),x([]),o().fire({icon:"error",title:"Error",text:"Failed to load leaves. Please try again."})}finally{h(!1)}},j=async()=>{try{if(!p.date||!p.reason.trim())return void o().fire({icon:"error",title:"Validation Error",text:"Please fill in all required fields."});let t=new Date(p.date),s=new Date;if(s.setHours(0,0,0,0),t<s)return void o().fire({icon:"error",title:"Invalid Date",text:"Cannot create leave for past dates."});if(d.find(e=>e.date.toDateString()===t.toDateString()))return void o().fire({icon:"error",title:"Duplicate Leave",text:"Leave already exists for this date."});y(!0);let{createAdminLeave:r}=await a.e(9567).then(a.bind(a,9567));await r({date:t,reason:p.reason.trim(),type:p.type,createdBy:(null==e?void 0:e.email)||"admin"}),await v(),o().fire({icon:"success",title:"Leave Created!",text:"Admin leave created for ".concat(t.toLocaleDateString(),"."),timer:3e3,showConfirmButton:!1}),b({date:"",reason:"",type:"holiday"}),u(!1)}catch(e){console.error("Error creating leave:",e),o().fire({icon:"error",title:"Creation Failed",text:"Failed to create leave. Please try again."})}finally{y(!1)}},N=async e=>{try{if((await o().fire({title:"Delete Leave",text:"Are you sure you want to delete this leave?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Delete",cancelButtonText:"Cancel"})).isConfirmed){let{deleteAdminLeave:t}=await a.e(9567).then(a.bind(a,9567));await t(e),await v(),o().fire({icon:"success",title:"Leave Deleted",text:"Leave has been deleted successfully.",timer:2e3,showConfirmButton:!1})}}catch(e){console.error("Error deleting leave:",e),o().fire({icon:"error",title:"Delete Failed",text:"Failed to delete leave. Please try again."})}},w=e=>{switch(e){case"holiday":return"bg-green-100 text-green-800";case"maintenance":return"bg-blue-100 text-blue-800";case"emergency":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"holiday":return"fas fa-calendar-day text-green-500";case"maintenance":return"fas fa-tools text-blue-500";case"emergency":return"fas fa-exclamation-triangle text-red-500";default:return"fas fa-calendar text-gray-500"}};return t||m?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading leaves..."})]})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(n(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Leave Management"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-gray-700",children:["Total: ",d.length]}),(0,s.jsxs)("button",{onClick:()=>{if(0===d.length)return void o().fire({icon:"warning",title:"No Data",text:"No leaves to export."});let e=d.map(e=>({Date:e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),Reason:e.reason,Type:e.type.charAt(0).toUpperCase()+e.type.slice(1),"Created By":e.createdBy,"Created At":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():new Date(e.createdAt).toLocaleDateString()}));(0,c.Bf)(e,"admin-leaves"),o().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(d.length," leaves to CSV file."),timer:2e3,showConfirmButton:!1})},className:"bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>u(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add Leave"]}),(0,s.jsxs)("button",{onClick:v,className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:0===d.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("i",{className:"fas fa-calendar-times text-gray-300 text-6xl mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No leaves scheduled"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your first admin leave to block work and withdrawals"}),(0,s.jsxs)("button",{onClick:()=>u(!0),className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Add First Leave"]})]}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reason"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created By"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.date.toLocaleDateString()}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:e.date.toLocaleDateString("en-US",{weekday:"long"})})]}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"text-sm text-gray-900 max-w-xs",children:e.reason})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(w(e.type)),children:[(0,s.jsx)("i",{className:"".concat(C(e.type)," mr-1")}),e.type.charAt(0).toUpperCase()+e.type.slice(1)]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.createdBy}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:e.createdAt.toLocaleDateString()})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("button",{onClick:()=>N(e.id),className:"text-red-600 hover:text-red-900",children:[(0,s.jsx)("i",{className:"fas fa-trash mr-1"}),"Delete"]})})]},e.id))})]})})})}),g&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:"Add Admin Leave"}),(0,s.jsx)("button",{onClick:()=>u(!1),className:"text-gray-500 hover:text-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date"}),(0,s.jsx)("input",{type:"date",value:p.date,onChange:e=>b(t=>({...t,date:e.target.value})),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Type"}),(0,s.jsxs)("select",{value:p.type,onChange:e=>b(t=>({...t,type:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"holiday",children:"Holiday"}),(0,s.jsx)("option",{value:"maintenance",children:"Maintenance"}),(0,s.jsx)("option",{value:"emergency",children:"Emergency"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Reason"}),(0,s.jsx)("textarea",{value:p.reason,onChange:e=>b(t=>({...t,reason:e.target.value})),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",placeholder:"Enter reason for leave..."})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)("button",{onClick:()=>u(!1),className:"px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300",children:"Cancel"}),(0,s.jsx)("button",{onClick:j,disabled:f,className:"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-plus mr-2"}),"Create Leave"]})})]})]})})]})}},5410:(e,t,a)=>{Promise.resolve().then(a.bind(a,1577))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,6465,8441,1684,7358],()=>t(5410)),_N_E=e.O()}]);