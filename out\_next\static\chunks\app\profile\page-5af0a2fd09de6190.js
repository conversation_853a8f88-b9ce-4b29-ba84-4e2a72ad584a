(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{1510:(e,a,s)=>{Promise.resolve().then(s.bind(s,3246))},3246:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>f});var t=s(5155),r=s(2115),l=s(6874),i=s.n(l),n=s(6681),o=s(7460),d=s(3592),c=s(12),m=s(3004),u=s(8647),h=s(4752),x=s.n(h);function f(){var e;let{user:a,loading:s}=(0,n.hD)(),{hasBlockingNotifications:l,isChecking:h,markAllAsRead:f}=(0,o.J)((null==a?void 0:a.uid)||null);(0,r.useEffect)(()=>{s||a||(window.location.href="/login")},[a,s]);let[p,w]=(0,r.useState)(null),[b,g]=(0,r.useState)(!0),[j,N]=(0,r.useState)(!1),[v,y]=(0,r.useState)({name:"",email:"",mobile:"",currentPassword:"",newPassword:"",confirmPassword:""}),[P,C]=(0,r.useState)(!1),[k,E]=(0,r.useState)(!1),[S,F]=(0,r.useState)(!1),[B,D]=(0,r.useState)(!1),[L,R]=(0,r.useState)(!1);(0,r.useEffect)(()=>{a&&U()},[a]);let U=async()=>{try{g(!0);let e=await (0,d.getUserData)(a.uid);w(e),y({name:(null==e?void 0:e.name)||"",email:(null==e?void 0:e.email)||"",mobile:(null==e?void 0:e.mobile)||"",currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){console.error("Error loading user data:",e),x().fire({icon:"error",title:"Error",text:"Failed to load profile data. Please try again."})}finally{g(!1)}},T=async()=>{if(!v.name.trim())return void x().fire({icon:"error",title:"Validation Error",text:"Name is required"});if(v.mobile&&!/^[6-9]\d{9}$/.test(v.mobile))return void x().fire({icon:"error",title:"Validation Error",text:"Please enter a valid 10-digit mobile number"});if(v.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.email))return void x().fire({icon:"error",title:"Validation Error",text:"Please enter a valid email address"});if(L){if(!v.currentPassword)return void x().fire({icon:"error",title:"Validation Error",text:"Current password is required to change password"});if(!v.newPassword||v.newPassword.length<6)return void x().fire({icon:"error",title:"Validation Error",text:"New password must be at least 6 characters long"});if(v.newPassword!==v.confirmPassword)return void x().fire({icon:"error",title:"Validation Error",text:"New password and confirm password do not match"})}try{if(C(!0),L&&v.currentPassword&&v.newPassword)try{let e=m.IX.credential(a.email,v.currentPassword);await (0,m.kZ)(a,e),await (0,m.f3)(a,v.newPassword),x().fire({icon:"success",title:"Password Updated",text:"Your password has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(a){console.error("Error updating password:",a);let e="Failed to update password. Please try again.";"auth/wrong-password"===a.code?e="Current password is incorrect":"auth/too-many-requests"===a.code&&(e="Too many failed attempts. Please try again later."),x().fire({icon:"error",title:"Password Update Failed",text:e});return}if(v.email!==(null==p?void 0:p.email)&&v.email)try{await (0,m.Ww)(a,v.email)}catch(a){console.error("Error updating email:",a);let e="Failed to update email. Please try again.";"auth/email-already-in-use"===a.code?e="This email is already in use by another account":"auth/requires-recent-login"===a.code&&(e="Please log out and log back in before changing your email"),x().fire({icon:"error",title:"Email Update Failed",text:e});return}let e={name:v.name.trim(),mobile:v.mobile};v.email!==(null==p?void 0:p.email)&&v.email&&(e.email=v.email),await (0,d.b6)(a.uid,e),w(a=>a?{...a,...e}:null),N(!1),R(!1),x().fire({icon:"success",title:"Profile Updated",text:"Your profile has been updated successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error updating profile:",e),x().fire({icon:"error",title:"Update Failed",text:"Failed to update profile. Please try again."})}finally{C(!1)}};return s||b||h?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner mb-4"}),(0,t.jsx)("p",{className:"text-white",children:s?"Loading...":h?"Checking notifications...":"Loading profile..."})]})}):l&&a?(0,t.jsx)(u.A,{userId:a.uid,onAllRead:f}):(0,t.jsxs)("div",{className:"min-h-screen p-4",children:[(0,t.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(i(),{href:"/dashboard",className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:"My Profile"}),(0,t.jsxs)("button",{onClick:()=>{(0,c._f)(null==a?void 0:a.uid,"/login")},className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-sign-out-alt mr-2"}),"Logout"]})]})}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white",children:[(0,t.jsx)("i",{className:"fas fa-user mr-2"}),"Profile Information"]}),!j&&(0,t.jsxs)("button",{onClick:()=>{N(!0)},className:"glass-button px-4 py-2 text-white",children:[(0,t.jsx)("i",{className:"fas fa-edit mr-2"}),"Edit"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Full Name"}),j?(0,t.jsx)("input",{type:"text",value:v.name,onChange:e=>y(a=>({...a,name:e.target.value})),className:"form-input",placeholder:"Enter your full name"}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.name)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Email Address"}),j?(0,t.jsx)("input",{type:"email",value:v.email,onChange:e=>y(a=>({...a,email:e.target.value})),className:"form-input",placeholder:"Enter your email address"}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.email)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Mobile Number"}),j?(0,t.jsx)("input",{type:"tel",value:v.mobile,onChange:e=>y(a=>({...a,mobile:e.target.value})),className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10}):(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.mobile)||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Member Since"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p||null==(e=p.joinedDate)?void 0:e.toLocaleDateString())||"Unknown"})]}),j&&(0,t.jsxs)("div",{className:"border-t border-white/20 pt-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Password"}),(0,t.jsxs)("button",{onClick:()=>R(!L),className:"glass-button px-4 py-2 text-white ".concat(L?"bg-red-500/20":"bg-blue-500/20"),children:[(0,t.jsx)("i",{className:"fas ".concat(L?"fa-times":"fa-key"," mr-2")}),L?"Cancel":"Change Password"]})]}),L&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:k?"text":"password",value:v.currentPassword,onChange:e=>y(a=>({...a,currentPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter your current password"}),(0,t.jsx)("button",{type:"button",onClick:()=>E(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(k?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:S?"text":"password",value:v.newPassword,onChange:e=>y(a=>({...a,newPassword:e.target.value})),className:"form-input pr-12",placeholder:"Enter new password (min 6 characters)"}),(0,t.jsx)("button",{type:"button",onClick:()=>F(!S),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(S?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:B?"text":"password",value:v.confirmPassword,onChange:e=>y(a=>({...a,confirmPassword:e.target.value})),className:"form-input pr-12",placeholder:"Confirm your new password"}),(0,t.jsx)("button",{type:"button",onClick:()=>D(!B),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,t.jsx)("i",{className:"fas ".concat(B?"fa-eye-slash":"fa-eye")})})]})]}),(0,t.jsx)("div",{className:"bg-yellow-500/20 p-3 rounded-lg",children:(0,t.jsxs)("p",{className:"text-yellow-300 text-sm",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),"Password must be at least 6 characters long. You will need to log in again after changing your password."]})})]})]})]}),j&&(0,t.jsxs)("div",{className:"flex gap-4 mt-6",children:[(0,t.jsx)("button",{onClick:T,disabled:P,className:"btn-primary flex-1",children:P?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})}),(0,t.jsxs)("button",{onClick:()=>{N(!1),R(!1),y({name:(null==p?void 0:p.name)||"",email:(null==p?void 0:p.email)||"",mobile:(null==p?void 0:p.mobile)||"",currentPassword:"",newPassword:"",confirmPassword:""})},className:"btn-secondary flex-1",children:[(0,t.jsx)("i",{className:"fas fa-times mr-2"}),"Cancel"]})]})]}),(0,t.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-crown mr-2"}),"Plan Information"]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Current Plan"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:(null==p?void 0:p.plan)||"Trial"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Active Days"}),(0,t.jsxs)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:[(null==p?void 0:p.activeDays)||0," days"]})]}),(null==p?void 0:p.planExpiry)&&(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Plan Expires"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg",children:p.planExpiry.toLocaleDateString()})]})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)(i(),{href:"/plans",className:"btn-primary",children:[(0,t.jsx)("i",{className:"fas fa-upgrade mr-2"}),"Upgrade Plan"]})})]}),(0,t.jsxs)("div",{className:"glass-card p-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,t.jsx)("i",{className:"fas fa-users mr-2"}),"Referral Information"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Your Referral Code"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg flex-1 font-mono",children:(null==p?void 0:p.referralCode)||"Not generated"}),(0,t.jsx)("button",{onClick:()=>{(null==p?void 0:p.referralCode)&&(navigator.clipboard.writeText(p.referralCode),x().fire({icon:"success",title:"Copied!",text:"Referral code copied to clipboard",timer:1500,showConfirmButton:!1}))},className:"glass-button px-4 py-2 text-white",title:"Copy referral code",children:(0,t.jsx)("i",{className:"fas fa-copy"})})]})]}),(null==p?void 0:p.referredBy)&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-white font-medium mb-2",children:"Referred By"}),(0,t.jsx)("p",{className:"text-white/80 bg-white/10 p-3 rounded-lg font-mono",children:p.referredBy})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("button",{onClick:()=>{if(null==p?void 0:p.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(p.referralCode);navigator.share?navigator.share({title:"Join MyTube and Start Earning",text:"Join MyTube using my referral code and start earning money by watching videos!",url:e}):(navigator.clipboard.writeText(e),x().fire({icon:"success",title:"Link Copied!",text:"Referral link copied to clipboard",timer:2e3,showConfirmButton:!1}))}},className:"btn-primary flex-1",children:[(0,t.jsx)("i",{className:"fas fa-share mr-2"}),"Share Referral Link"]}),(0,t.jsxs)(i(),{href:"/refer",className:"btn-secondary flex-1 text-center",children:[(0,t.jsx)("i",{className:"fas fa-users mr-2"}),"View Referrals"]})]})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3592,1018,8441,1684,7358],()=>a(1510)),_N_E=e.O()}]);