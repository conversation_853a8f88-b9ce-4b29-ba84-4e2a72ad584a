{"/_not-found/page": "/_not-found", "/admin/leaves/page": "/admin/leaves", "/admin/page": "/admin", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/notifications/page": "/admin/notifications", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/users/page": "/admin/users", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/dashboard/page": "/dashboard", "/admin/upload-users/page": "/admin/upload-users", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/forgot-password/page": "/forgot-password", "/debug-firestore-issue/page": "/debug-firestore-issue", "/login/page": "/login", "/page": "/", "/debug-registration-simple/page": "/debug-registration-simple", "/plans/page": "/plans", "/register/page": "/register", "/refer/page": "/refer", "/profile/page": "/profile", "/registration-diagnostics/page": "/registration-diagnostics", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/debug-registration/page": "/debug-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/debug-firestore/page": "/debug-firestore", "/test-registration/page": "/test-registration", "/test-videos/page": "/test-videos", "/transactions/page": "/transactions", "/wallet/page": "/wallet", "/admin/login/page": "/admin/login", "/admin/daily-active-days/page": "/admin/daily-active-days", "/work/page": "/work"}