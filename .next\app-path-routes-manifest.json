{"/admin/fix-permissions/page": "/admin/fix-permissions", "/_not-found/page": "/_not-found", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/notifications/page": "/admin/notifications", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/settings/page": "/admin/settings", "/admin/setup/page": "/admin/setup", "/admin/transactions/page": "/admin/transactions", "/admin/test-blocking/page": "/admin/test-blocking", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/admin/users/page": "/admin/users", "/clear-cache/page": "/clear-cache", "/admin/upload-users/page": "/admin/upload-users", "/debug-firestore-issue/page": "/debug-firestore-issue", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/forgot-password/page": "/forgot-password", "/debug-registration/page": "/debug-registration", "/login/page": "/login", "/page": "/", "/plans/page": "/plans", "/refer/page": "/refer", "/profile/page": "/profile", "/register/page": "/register", "/reset-password/page": "/reset-password", "/registration-diagnostics/page": "/registration-diagnostics", "/support/page": "/support", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/test-registration/page": "/test-registration", "/test-firebase/page": "/test-firebase", "/test-firestore/page": "/test-firestore", "/test-reg-simple/page": "/test-reg-simple", "/test-simple-registration/page": "/test-simple-registration", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/work/page": "/work", "/admin/leaves/page": "/admin/leaves", "/transactions/page": "/transactions"}