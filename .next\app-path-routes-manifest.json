{"/_not-found/page": "/_not-found", "/admin/fix-permissions/page": "/admin/fix-permissions", "/admin/leaves/page": "/admin/leaves", "/admin/login/page": "/admin/login", "/admin/page": "/admin", "/admin/daily-active-days/page": "/admin/daily-active-days", "/admin/notifications/page": "/admin/notifications", "/admin/simple-upload/page": "/admin/simple-upload", "/admin/fix-active-days/page": "/admin/fix-active-days", "/admin/settings/page": "/admin/settings", "/admin/test-blocking/page": "/admin/test-blocking", "/admin/transactions/page": "/admin/transactions", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/admin/upload-users/page": "/admin/upload-users", "/dashboard/page": "/dashboard", "/admin/withdrawals/page": "/admin/withdrawals", "/clear-cache/page": "/clear-cache", "/debug-firestore-issue/page": "/debug-firestore-issue", "/forgot-password/page": "/forgot-password", "/debug-firestore/page": "/debug-firestore", "/debug-registration-simple/page": "/debug-registration-simple", "/login/page": "/login", "/debug-registration/page": "/debug-registration", "/page": "/", "/plans/page": "/plans", "/profile/page": "/profile", "/registration-diagnostics/page": "/registration-diagnostics", "/refer/page": "/refer", "/reset-password/page": "/reset-password", "/support/page": "/support", "/test-firestore/page": "/test-firestore", "/test-firebase-connection/page": "/test-firebase-connection", "/test-firebase/page": "/test-firebase", "/test-firebase-connectivity/page": "/test-firebase-connectivity", "/register/page": "/register", "/test-simple-registration/page": "/test-simple-registration", "/test-registration/page": "/test-registration", "/test-reg-simple/page": "/test-reg-simple", "/test-videos/page": "/test-videos", "/wallet/page": "/wallet", "/transactions/page": "/transactions", "/work/page": "/work"}