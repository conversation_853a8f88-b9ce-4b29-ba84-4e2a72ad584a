(()=>{var e={};e.id=2116,e.ids=[2116],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),o=r(63385),i=r(75535),a=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,o.xI)(n),c=(0,i.aU)(n);(0,a.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},37823:(e,t,r)=>{Promise.resolve().then(r.bind(r,64248))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{G9:()=>u,M4:()=>c,_f:()=>l,g4:()=>i});var s=r(33784),o=r(77567);function i(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,o=`daily_watch_times_${e}_${t}`,i=localStorage.getItem(`backup_timestamp_${e}`);if(!i)return!1;if(new Date(parseInt(i)).toDateString()!==t)return a(e),!1;let n=localStorage.getItem(`backup_${r}`),l=localStorage.getItem(`backup_${s}`),c=localStorage.getItem(`backup_${o}`),u=!1;if(n&&(localStorage.setItem(r,n),u=!0),l&&(localStorage.setItem(s,l),u=!0),c&&(localStorage.setItem(o,c),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),a(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function a(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,o=`daily_watch_times_${e}_${t}`,i=localStorage.getItem(r),a=localStorage.getItem(s),n=localStorage.getItem(o);i&&localStorage.setItem(`backup_${r}`,i),a&&localStorage.setItem(`backup_${s}`,a),n&&localStorage.setItem(`backup_${o}`,n),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:i,watchTimesCount:a?JSON.parse(a).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,t="/login"){try{if((await o.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await s.j2.signOut(),o.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e,t="/login",r=!0){try{e&&n(e,r),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let r=localStorage.getItem(e);r&&new Date(parseInt(r)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64248:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),o=r(43210),i=r(85814),a=r.n(i),n=r(30474),l=r(63385),c=r(33784),u=r(87979),d=r(77567);function m(){let{user:e,loading:t}=(0,u.hD)(),[r,i]=(0,o.useState)(""),[m,p]=(0,o.useState)(""),[h,g]=(0,o.useState)(!1),[x,f]=(0,o.useState)(!1),b=async e=>{if(e.preventDefault(),!r||!m)return void d.A.fire({icon:"error",title:"Error",text:"Please fill in all fields"});g(!0);try{let e=(await (0,l.x9)(c.j2,r,m)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await c.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(t){console.error("Admin login error:",t);let e="An error occurred during login";if(t.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(t.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=t.message||"Admin login failed"}d.A.fire({icon:"error",title:"Admin Login Failed",text:e}),p("")}finally{g(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,s.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,s.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,s.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,s.jsx)("input",{type:"email",id:"email",value:r,onChange:e=>i(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,s.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:x?"text":"password",id:"password",value:m,onChange:e=>p(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>f(!x),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,s.jsx)("i",{className:`fas ${x?"fa-eye-slash":"fa-eye"}`})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full btn-primary flex items-center justify-center",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,s.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,s.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,s.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)(a(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},64821:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c});var s=r(65239),o=r(48088),i=r(88170),a=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76158)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/login/page",pathname:"/admin/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76158:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\login\\page.tsx","default")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>a,hD:()=>i,wC:()=>n});var s=r(43210);r(63385),r(33784);var o=r(51278);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),a=async()=>{try{await (0,o.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:a}}function a(){let{user:e,loading:t}=i();return{user:e,loading:t}}function n(){let{user:e,loading:t}=i(),[r,o]=(0,s.useState)(!1),[a,n]=(0,s.useState)(!0);return{user:e,loading:t||a,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98503:(e,t,r)=>{Promise.resolve().then(r.bind(r,76158))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441],()=>r(64821));module.exports=s})();