(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3790],{12:(e,t,s)=>{"use strict";s.d(t,{G9:()=>m,M4:()=>d,_f:()=>o,g4:()=>n});var a=s(6104),r=s(4752),l=s.n(r);function n(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),l=localStorage.getItem("backup_timestamp_".concat(e));if(!l)return!1;if(new Date(parseInt(l)).toDateString()!==t)return i(e),!1;let n=localStorage.getItem("backup_".concat(s)),c=localStorage.getItem("backup_".concat(a)),o=localStorage.getItem("backup_".concat(r)),d=!1;if(n&&(localStorage.setItem(s,n),d=!0),c&&(localStorage.setItem(a,c),d=!0),o&&(localStorage.setItem(r,o),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:o?JSON.parse(o).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),l=localStorage.getItem(s),n=localStorage.getItem(a),i=localStorage.getItem(r);l&&localStorage.setItem("backup_".concat(s),l),n&&localStorage.setItem("backup_".concat(a),n),i&&localStorage.setItem("backup_".concat(r),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:l,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await l().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await a.j2.signOut(),l().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),l().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,s),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function m(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let s=localStorage.getItem(e);s&&new Date(parseInt(s)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},3704:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5155),r=s(2115),l=s(6874),n=s.n(l),i=s(6681),c=s(3592),o=s(4752),d=s.n(o);function m(){let{user:e,loading:t}=(0,i.Nu)(),[s,l]=(0,r.useState)([]),[o,m]=(0,r.useState)([]),[h,x]=(0,r.useState)(!0),[u,g]=(0,r.useState)(1),[f,p]=(0,r.useState)(1),[j,w]=(0,r.useState)({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""}),[b,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{e&&y()},[e]),(0,r.useEffect)(()=>{v()},[s,j]);let y=async()=>{try{x(!0);let t=await (0,c.I0)(e.uid,100);l(t)}catch(e){console.error("Error loading transactions:",e),d().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{x(!1)}},v=()=>{let e=[...s];if(j.type&&(e=e.filter(e=>e.type===j.type)),j.status&&(e=e.filter(e=>e.status===j.status)),j.dateFrom){let t=new Date(j.dateFrom);e=e.filter(e=>e.date>=t)}if(j.dateTo){let t=new Date(j.dateTo);t.setHours(23,59,59,999),e=e.filter(e=>e.date<=t)}if(j.searchTerm){let t=j.searchTerm.toLowerCase();e=e.filter(e=>e.description.toLowerCase().includes(t)||e.type.toLowerCase().includes(t))}m(e),p(Math.ceil(e.length/20)),g(1)},S=(e,t)=>{w(s=>({...s,[e]:t}))},_=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),k=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-400";case"withdrawal":return"fas fa-download text-red-400";case"bonus":return"fas fa-gift text-yellow-400";case"referral":return"fas fa-users text-blue-400";default:return"fas fa-exchange-alt text-white"}},C=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},T=(u-1)*20,D=o.slice(T,T+20);return t||h?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading transactions..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n(),{href:"/wallet",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Wallet"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Transaction History"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>N(!b),className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-filter mr-2"}),"Filters"]}),(0,a.jsxs)("button",{onClick:y,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Transactions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:o.length})]}),(0,a.jsx)("i",{className:"fas fa-list text-blue-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Earned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:_(o.filter(e=>e.amount>0).reduce((e,t)=>e+t.amount,0))})]}),(0,a.jsx)("i",{className:"fas fa-arrow-up text-green-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Withdrawn"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:_(Math.abs(o.filter(e=>e.amount<0).reduce((e,t)=>e+t.amount,0)))})]}),(0,a.jsx)("i",{className:"fas fa-arrow-down text-red-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"This Month"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:o.filter(e=>{let t=new Date,s=new Date(e.date);return s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear()}).length})]}),(0,a.jsx)("i",{className:"fas fa-calendar text-yellow-400 text-2xl"})]})})]}),b&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-filter mr-2"}),"Filter Transactions"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",value:j.searchTerm,onChange:e=>S("searchTerm",e.target.value),placeholder:"Search description or type...",className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type"}),(0,a.jsxs)("select",{value:j.type,onChange:e=>S("type",e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"All Types"}),(0,a.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,a.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,a.jsx)("option",{value:"bonus",children:"Bonus"}),(0,a.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Status"}),(0,a.jsxs)("select",{value:j.status,onChange:e=>S("status",e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"From Date"}),(0,a.jsx)("input",{type:"date",value:j.dateFrom,onChange:e=>S("dateFrom",e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"To Date"}),(0,a.jsx)("input",{type:"date",value:j.dateTo,onChange:e=>S("dateTo",e.target.value),className:"form-input"})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>{if(0===o.length)return void d().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=new Blob([["Date,Type,Description,Amount,Status",...o.map(e=>[e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),e.type,'"'.concat(e.description,'"'),e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="transactions_".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},className:"btn-primary w-full",disabled:0===o.length,children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]})})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>{w({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""})},className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Filters"]}),(0,a.jsxs)("span",{className:"text-white/60 text-sm flex items-center",children:["Showing ",o.length," of ",s.length," transactions"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-history mr-2"}),"Transactions"]}),o.length>0&&(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Page ",u," of ",f]})]}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"fas fa-receipt text-white/30 text-6xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 text-lg mb-2",children:"No transactions found"}),(0,a.jsx)("p",{className:"text-white/40 text-sm",children:0===s.length?"You haven't made any transactions yet":"Try adjusting your filters"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/20",children:[(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Date & Time"}),(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Type"}),(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Description"}),(0,a.jsx)("th",{className:"text-right text-white font-medium py-3 px-2",children:"Amount"}),(0,a.jsx)("th",{className:"text-center text-white font-medium py-3 px-2",children:"Status"})]})}),(0,a.jsx)("tbody",{children:D.map(e=>(0,a.jsxs)("tr",{className:"border-b border-white/10 hover:bg-white/5",children:[(0,a.jsxs)("td",{className:"py-4 px-2",children:[(0,a.jsx)("div",{className:"text-white text-sm",children:e.date.toLocaleDateString()}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:e.date.toLocaleTimeString()})]}),(0,a.jsx)("td",{className:"py-4 px-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"".concat(k(e.type)," mr-2")}),(0,a.jsx)("span",{className:"text-white text-sm",children:C(e.type)})]})}),(0,a.jsx)("td",{className:"py-4 px-2",children:(0,a.jsx)("p",{className:"text-white text-sm",children:e.description})}),(0,a.jsx)("td",{className:"py-4 px-2 text-right",children:(0,a.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",_(e.amount)]})}),(0,a.jsx)("td",{className:"py-4 px-2 text-center",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),(0,a.jsx)("div",{className:"md:hidden space-y-3",children:D.map(e=>(0,a.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"".concat(k(e.type)," mr-2")}),(0,a.jsx)("span",{className:"text-white font-medium text-sm",children:C(e.type)})]}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,a.jsx)("p",{className:"text-white text-sm mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-white/60 text-xs",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,a.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",_(e.amount)]})]})]},e.id))}),f>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center mt-6 gap-2",children:[(0,a.jsx)("button",{onClick:()=>g(e=>Math.max(1,e-1)),disabled:1===u,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,a.jsx)("i",{className:"fas fa-chevron-left"})}),(0,a.jsx)("div",{className:"flex gap-1",children:Array.from({length:Math.min(5,f)},(e,t)=>{let s;return s=f<=5||u<=3?t+1:u>=f-2?f-4+t:u-2+t,(0,a.jsx)("button",{onClick:()=>g(s),className:"px-3 py-2 text-sm rounded ".concat(u===s?"bg-blue-500 text-white":"glass-button text-white"),children:s},s)})}),(0,a.jsx)("button",{onClick:()=>g(e=>Math.min(f,e+1)),disabled:u===f,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,a.jsx)("i",{className:"fas fa-chevron-right"})})]})]})]})]})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>o,j2:()=>c});var a=s(3915),r=s(3004),l=s(5317),n=s(858);let i=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,r.xI)(i),o=(0,l.aU)(i);(0,n.c7)(i)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>i,wC:()=>o});var a=s(2115),r=s(3004),l=s(6104),n=s(12);function i(){let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(l.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=i();return(0,a.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function o(){let{user:e,loading:t}=i(),[s,r]=(0,a.useState)(!1),[l,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||l,isAdmin:s}}},9137:(e,t,s)=>{Promise.resolve().then(s.bind(s,3704))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(9137)),_N_E=e.O()}]);