(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3790],{12:(e,t,s)=>{"use strict";s.d(t,{M4:()=>c,_f:()=>r});var a=s(6104),l=s(4752),i=s.n(l);function n(e){try{Object.keys(localStorage).forEach(t=>{(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await a.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&n(e),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}},3704:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(5155),l=s(2115),i=s(6874),n=s.n(i),r=s(6681),c=s(3592),o=s(4752),d=s.n(o);function x(){let{user:e,loading:t}=(0,r.Nu)(),[s,i]=(0,l.useState)([]),[o,x]=(0,l.useState)([]),[m,h]=(0,l.useState)(!0),[u,f]=(0,l.useState)(1),[p,g]=(0,l.useState)(1),[j,w]=(0,l.useState)({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""}),[N,b]=(0,l.useState)(!1);(0,l.useEffect)(()=>{e&&y()},[e]),(0,l.useEffect)(()=>{v()},[s,j]);let y=async()=>{try{h(!0);let t=await (0,c.I0)(e.uid,100);i(t)}catch(e){console.error("Error loading transactions:",e),d().fire({icon:"error",title:"Error",text:"Failed to load transactions. Please try again."})}finally{h(!1)}},v=()=>{let e=[...s];if(j.type&&(e=e.filter(e=>e.type===j.type)),j.status&&(e=e.filter(e=>e.status===j.status)),j.dateFrom){let t=new Date(j.dateFrom);e=e.filter(e=>e.date>=t)}if(j.dateTo){let t=new Date(j.dateTo);t.setHours(23,59,59,999),e=e.filter(e=>e.date<=t)}if(j.searchTerm){let t=j.searchTerm.toLowerCase();e=e.filter(e=>e.description.toLowerCase().includes(t)||e.type.toLowerCase().includes(t))}x(e),g(Math.ceil(e.length/20)),f(1)},S=(e,t)=>{w(s=>({...s,[e]:t}))},C=e=>null==e||isNaN(e)?"₹0.00":new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0,maximumFractionDigits:2}).format(e),T=e=>{switch(e){case"video_earning":return"fas fa-play-circle text-green-400";case"withdrawal":return"fas fa-download text-red-400";case"bonus":return"fas fa-gift text-yellow-400";case"referral":return"fas fa-users text-blue-400";default:return"fas fa-exchange-alt text-white"}},k=e=>{switch(e){case"video_earning":return"Video Earning";case"withdrawal":return"Withdrawal";case"bonus":return"Bonus";case"referral":return"Referral";default:return e.charAt(0).toUpperCase()+e.slice(1)}},D=(u-1)*20,_=o.slice(D,D+20);return t||m?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading transactions..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-4",children:[(0,a.jsx)("header",{className:"glass-card p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(n(),{href:"/wallet",className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Wallet"]}),(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"Transaction History"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>b(!N),className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-filter mr-2"}),"Filters"]}),(0,a.jsxs)("button",{onClick:y,className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Transactions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:o.length})]}),(0,a.jsx)("i",{className:"fas fa-list text-blue-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Earned"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:C(o.filter(e=>e.amount>0).reduce((e,t)=>e+t.amount,0))})]}),(0,a.jsx)("i",{className:"fas fa-arrow-up text-green-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"Total Withdrawn"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:C(Math.abs(o.filter(e=>e.amount<0).reduce((e,t)=>e+t.amount,0)))})]}),(0,a.jsx)("i",{className:"fas fa-arrow-down text-red-400 text-2xl"})]})}),(0,a.jsx)("div",{className:"glass-card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white/60 text-sm",children:"This Month"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:o.filter(e=>{let t=new Date,s=new Date(e.date);return s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear()}).length})]}),(0,a.jsx)("i",{className:"fas fa-calendar text-yellow-400 text-2xl"})]})})]}),N&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white mb-4",children:[(0,a.jsx)("i",{className:"fas fa-filter mr-2"}),"Filter Transactions"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",value:j.searchTerm,onChange:e=>S("searchTerm",e.target.value),placeholder:"Search description or type...",className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Type"}),(0,a.jsxs)("select",{value:j.type,onChange:e=>S("type",e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"All Types"}),(0,a.jsx)("option",{value:"video_earning",children:"Video Earning"}),(0,a.jsx)("option",{value:"withdrawal",children:"Withdrawal"}),(0,a.jsx)("option",{value:"bonus",children:"Bonus"}),(0,a.jsx)("option",{value:"referral",children:"Referral"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"Status"}),(0,a.jsxs)("select",{value:j.status,onChange:e=>S("status",e.target.value),className:"form-input",children:[(0,a.jsx)("option",{value:"",children:"All Status"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"pending",children:"Pending"}),(0,a.jsx)("option",{value:"failed",children:"Failed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"From Date"}),(0,a.jsx)("input",{type:"date",value:j.dateFrom,onChange:e=>S("dateFrom",e.target.value),className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-white font-medium mb-2",children:"To Date"}),(0,a.jsx)("input",{type:"date",value:j.dateTo,onChange:e=>S("dateTo",e.target.value),className:"form-input"})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("button",{onClick:()=>{if(0===o.length)return void d().fire({icon:"warning",title:"No Data",text:"No transactions to export."});let e=new Blob([["Date,Type,Description,Amount,Status",...o.map(e=>[e.date instanceof Date?e.date.toLocaleDateString():new Date(e.date).toLocaleDateString(),e.type,'"'.concat(e.description,'"'),e.amount,e.status].join(","))].join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="transactions_".concat(new Date().toISOString().split("T")[0],".csv"),s.click(),window.URL.revokeObjectURL(t)},className:"btn-primary w-full",disabled:0===o.length,children:[(0,a.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]})})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>{w({type:"",status:"",dateFrom:"",dateTo:"",searchTerm:""})},className:"glass-button px-4 py-2 text-white",children:[(0,a.jsx)("i",{className:"fas fa-times mr-2"}),"Clear Filters"]}),(0,a.jsxs)("span",{className:"text-white/60 text-sm flex items-center",children:["Showing ",o.length," of ",s.length," transactions"]})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-white",children:[(0,a.jsx)("i",{className:"fas fa-history mr-2"}),"Transactions"]}),o.length>0&&(0,a.jsxs)("p",{className:"text-white/60 text-sm",children:["Page ",u," of ",p]})]}),0===o.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"fas fa-receipt text-white/30 text-6xl mb-4"}),(0,a.jsx)("p",{className:"text-white/60 text-lg mb-2",children:"No transactions found"}),(0,a.jsx)("p",{className:"text-white/40 text-sm",children:0===s.length?"You haven't made any transactions yet":"Try adjusting your filters"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"hidden md:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-white/20",children:[(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Date & Time"}),(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Type"}),(0,a.jsx)("th",{className:"text-left text-white font-medium py-3 px-2",children:"Description"}),(0,a.jsx)("th",{className:"text-right text-white font-medium py-3 px-2",children:"Amount"}),(0,a.jsx)("th",{className:"text-center text-white font-medium py-3 px-2",children:"Status"})]})}),(0,a.jsx)("tbody",{children:_.map(e=>(0,a.jsxs)("tr",{className:"border-b border-white/10 hover:bg-white/5",children:[(0,a.jsxs)("td",{className:"py-4 px-2",children:[(0,a.jsx)("div",{className:"text-white text-sm",children:e.date.toLocaleDateString()}),(0,a.jsx)("div",{className:"text-white/60 text-xs",children:e.date.toLocaleTimeString()})]}),(0,a.jsx)("td",{className:"py-4 px-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"".concat(T(e.type)," mr-2")}),(0,a.jsx)("span",{className:"text-white text-sm",children:k(e.type)})]})}),(0,a.jsx)("td",{className:"py-4 px-2",children:(0,a.jsx)("p",{className:"text-white text-sm",children:e.description})}),(0,a.jsx)("td",{className:"py-4 px-2 text-right",children:(0,a.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",C(e.amount)]})}),(0,a.jsx)("td",{className:"py-4 px-2 text-center",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})})]},e.id))})]})}),(0,a.jsx)("div",{className:"md:hidden space-y-3",children:_.map(e=>(0,a.jsxs)("div",{className:"p-4 bg-white/10 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"".concat(T(e.type)," mr-2")}),(0,a.jsx)("span",{className:"text-white font-medium text-sm",children:k(e.type)})]}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-400/20 text-green-400":"pending"===e.status?"bg-yellow-400/20 text-yellow-400":"failed"===e.status?"bg-red-400/20 text-red-400":"bg-gray-400/20 text-gray-400"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,a.jsx)("p",{className:"text-white text-sm mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-white/60 text-xs",children:[e.date.toLocaleDateString()," ",e.date.toLocaleTimeString()]}),(0,a.jsxs)("p",{className:"font-bold text-sm ".concat(e.amount>0?"text-green-400":"text-red-400"),children:[e.amount>0?"+":"",C(e.amount)]})]})]},e.id))}),p>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center mt-6 gap-2",children:[(0,a.jsx)("button",{onClick:()=>f(e=>Math.max(1,e-1)),disabled:1===u,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,a.jsx)("i",{className:"fas fa-chevron-left"})}),(0,a.jsx)("div",{className:"flex gap-1",children:Array.from({length:Math.min(5,p)},(e,t)=>{let s;return s=p<=5||u<=3?t+1:u>=p-2?p-4+t:u-2+t,(0,a.jsx)("button",{onClick:()=>f(s),className:"px-3 py-2 text-sm rounded ".concat(u===s?"bg-blue-500 text-white":"glass-button text-white"),children:s},s)})}),(0,a.jsx)("button",{onClick:()=>f(e=>Math.min(p,e+1)),disabled:u===p,className:"glass-button px-3 py-2 text-white disabled:opacity-50",children:(0,a.jsx)("i",{className:"fas fa-chevron-right"})})]})]})]})]})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>o,j2:()=>c});var a=s(3915),l=s(3004),i=s(5317),n=s(858);let r=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,l.xI)(r),o=(0,i.aU)(r);(0,n.c7)(r)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>r,wC:()=>o});var a=s(2115),l=s(3004),i=s(6104),n=s(12);function r(){let[e,t]=(0,a.useState)(null),[s,r]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,l.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),r(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),r(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=r();return(0,a.useEffect)(()=>{t||e||(window.location.href="/login")},[e,t]),{user:e,loading:t}}function o(){let{user:e,loading:t}=r(),[s,l]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");l(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||i,isAdmin:s}}},9137:(e,t,s)=>{Promise.resolve().then(s.bind(s,3704))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(9137)),_N_E=e.O()}]);