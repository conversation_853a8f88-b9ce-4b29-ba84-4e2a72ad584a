'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRequireAdmin } from '@/hooks/useAuth'
import { dailyActiveDaysIncrement, migrateQuickVideoAdvantageSystem, recalculateAllUsersActiveDays } from '@/lib/dataService'
import Swal from 'sweetalert2'

export default function DailyActiveDaysPage() {
  const { user, loading, isAdmin } = useRequireAdmin()
  const [isProcessing, setIsProcessing] = useState(false)
  const [lastResult, setLastResult] = useState<any>(null)
  const [isMigrating, setIsMigrating] = useState(false)
  const [migrationResult, setMigrationResult] = useState<any>(null)
  const [isRecalculating, setIsRecalculating] = useState(false)
  const [recalculationResult, setRecalculationResult] = useState<any>(null)

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="spinner w-8 h-8 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    )
  }

  const handleDailyIncrement = async () => {
    try {
      setIsProcessing(true)
      
      const result = await dailyActiveDaysIncrement()
      setLastResult(result)
      
      Swal.fire({
        icon: 'success',
        title: 'Daily Active Days Increment Completed!',
        html: `
          <div class="text-left">
            <p><strong>Incremented:</strong> ${result.incrementedCount} users</p>
            <p><strong>Skipped:</strong> ${result.skippedCount} users</p>
            <p><strong>Errors:</strong> ${result.errorCount} users</p>
            ${result.reason ? `<p><strong>Reason:</strong> ${result.reason}</p>` : ''}
          </div>
        `,
        timer: 5000,
        showConfirmButton: true
      })
      
    } catch (error) {
      console.error('Error running daily active days increment:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to run daily active days increment. Please try again.',
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleMigrateQuickVideo = async () => {
    try {
      setIsMigrating(true)

      const result = await migrateQuickVideoAdvantageSystem()
      setMigrationResult(result)

      Swal.fire({
        icon: 'success',
        title: 'Quick Video Migration Completed!',
        html: `
          <div class="text-left">
            <p><strong>Migrated:</strong> ${result.migratedCount} users</p>
            <p><strong>Skipped:</strong> ${result.skippedCount} users</p>
            <p><strong>Errors:</strong> ${result.errorCount} users</p>
          </div>
        `,
        timer: 5000,
        showConfirmButton: true
      })

    } catch (error) {
      console.error('Error running quick video migration:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to run quick video migration. Please try again.',
      })
    } finally {
      setIsMigrating(false)
    }
  }

  const handleRecalculateActiveDays = async () => {
    try {
      setIsRecalculating(true)

      const result = await recalculateAllUsersActiveDays()
      setRecalculationResult(result)

      Swal.fire({
        icon: 'success',
        title: 'Active Days Recalculation Completed!',
        html: `
          <div class="text-left">
            <p><strong>Recalculated:</strong> ${result.recalculatedCount} users</p>
            <p><strong>Errors:</strong> ${result.errorCount} users</p>
            <p class="text-sm text-gray-600 mt-2">All users now have correct active days based on registration date.</p>
          </div>
        `,
        timer: 5000,
        showConfirmButton: true
      })

    } catch (error) {
      console.error('Error running active days recalculation:', error)
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to recalculate active days. Please try again.',
      })
    } finally {
      setIsRecalculating(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Daily Active Days Management</h1>
              <p className="mt-2 text-gray-600">
                Manually trigger daily active days increment for all users
              </p>
            </div>
            <Link
              href="/admin"
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Admin
            </Link>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              <i className="fas fa-calendar-plus mr-2 text-blue-500"></i>
              Daily Active Days Increment
            </h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-blue-900 mb-2">How it works:</h3>
              <ul className="text-blue-800 text-sm space-y-1">
                <li>• Increments active days by 1 for all users (regardless of login status)</li>
                <li>• Skips users who are on approved leave today</li>
                <li>• Skips increment if today is an admin leave day</li>
                <li>• Only processes each user once per day</li>
                <li>• Preserves manually set active days by admin</li>
              </ul>
            </div>
            
            <button
              onClick={handleDailyIncrement}
              disabled={isProcessing}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <>
                  <div className="spinner w-4 h-4 inline-block mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <i className="fas fa-play mr-2"></i>
                  Run Daily Active Days Increment
                </>
              )}
            </button>
          </div>

          {/* Last Result */}
          {lastResult && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Last Execution Result</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{lastResult.incrementedCount}</div>
                    <div className="text-sm text-gray-600">Users Incremented</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{lastResult.skippedCount}</div>
                    <div className="text-sm text-gray-600">Users Skipped</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{lastResult.errorCount}</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </div>
                </div>
                {lastResult.reason && (
                  <div className="mt-4 text-center">
                    <span className="inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                      {lastResult.reason}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Information */}
          <div className="border-t pt-6 mt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Important Notes</h3>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <ul className="text-yellow-800 text-sm space-y-2">
                <li>• <strong>Automatic Execution:</strong> This process also runs automatically when users interact with the platform (once per day)</li>
                <li>• <strong>Manual Trigger:</strong> Use this page to manually trigger the process if needed</li>
                <li>• <strong>Safety:</strong> The process is safe to run multiple times per day - it will skip users already processed</li>
                <li>• <strong>Leave Days:</strong> Active days will not increment on admin leave days or user leave days</li>
                <li>• <strong>Manual Override:</strong> Users with manually set active days will still get daily increments</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Video Migration */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              <i className="fas fa-video mr-2 text-purple-500"></i>
              Quick Video Advantage Migration
            </h2>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-purple-900 mb-2">Migration Purpose:</h3>
              <ul className="text-purple-800 text-sm space-y-1">
                <li>• Converts old expiry date system to new remaining days system</li>
                <li>• Calculates remaining days from current expiry dates</li>
                <li>• Disables expired quick video advantages</li>
                <li>• Ensures proper daily decrement functionality</li>
                <li>• Safe to run multiple times (skips already migrated users)</li>
              </ul>
            </div>

            <button
              onClick={handleMigrateQuickVideo}
              disabled={isMigrating}
              className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isMigrating ? (
                <>
                  <div className="spinner w-4 h-4 inline-block mr-2"></div>
                  Migrating...
                </>
              ) : (
                <>
                  <i className="fas fa-sync-alt mr-2"></i>
                  Migrate Quick Video System
                </>
              )}
            </button>
          </div>

          {/* Migration Result */}
          {migrationResult && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Migration Result</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{migrationResult.migratedCount}</div>
                    <div className="text-sm text-gray-600">Users Migrated</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{migrationResult.skippedCount}</div>
                    <div className="text-sm text-gray-600">Users Skipped</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{migrationResult.errorCount}</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Active Days Recalculation */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              <i className="fas fa-calculator mr-2 text-orange-500"></i>
              Active Days Recalculation
            </h2>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-orange-900 mb-2">Fix Active Days Issues:</h3>
              <ul className="text-orange-800 text-sm space-y-1">
                <li>• Recalculates active days for all users based on correct formula</li>
                <li>• Fixes the "showing 3 days instead of 2" issue</li>
                <li>• Ensures Trial plans expire at exactly 3+ active days</li>
                <li>• Ensures other plans expire at exactly 31+ active days</li>
                <li>• Safe to run multiple times (only updates incorrect values)</li>
              </ul>
            </div>

            <button
              onClick={handleRecalculateActiveDays}
              disabled={isRecalculating}
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRecalculating ? (
                <>
                  <div className="spinner w-4 h-4 inline-block mr-2"></div>
                  Recalculating...
                </>
              ) : (
                <>
                  <i className="fas fa-calculator mr-2"></i>
                  Fix Active Days Calculation
                </>
              )}
            </button>
          </div>

          {/* Recalculation Result */}
          {recalculationResult && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recalculation Result</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{recalculationResult.recalculatedCount}</div>
                    <div className="text-sm text-gray-600">Users Fixed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{recalculationResult.errorCount}</div>
                    <div className="text-sm text-gray-600">Errors</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
