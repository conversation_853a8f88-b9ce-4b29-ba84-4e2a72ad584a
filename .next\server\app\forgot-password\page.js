(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3340:(e,t,r)=>{Promise.resolve().then(r.bind(r,10038))},4573:e=>{"use strict";e.exports=require("node:buffer")},10038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),o=r(43210),a=r(85814),i=r.n(a),n=r(30474),l=r(63385),c=r(33784),u=r(87979),d=r(77567);function m(){let{user:e,loading:t}=(0,u.hD)(),[r,a]=(0,o.useState)(""),[m,p]=(0,o.useState)(!1),[h,x]=(0,o.useState)(!1),f=async e=>{if(e.preventDefault(),!r.trim())return void d.A.fire({icon:"error",title:"Email Required",text:"Please enter your email address"});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return void d.A.fire({icon:"error",title:"Invalid Email",text:"Please enter a valid email address"});p(!0);try{await (0,l.J1)(c.j2,r.trim().toLowerCase(),{url:`${window.location.origin}/login`,handleCodeInApp:!1}),x(!0),d.A.fire({icon:"success",title:"Reset Email Sent!",html:`
          <p>We've sent a password reset link to:</p>
          <p class="font-semibold text-blue-600">${r}</p>
          <p class="mt-4 text-sm text-gray-600">
            Please check your email and click the link to reset your password.
            If you don't see the email, check your spam folder.
          </p>
        `,confirmButtonText:"Got it!",confirmButtonColor:"#3b82f6"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while sending the reset email";switch(t.code){case"auth/user-not-found":e="No account found with this email address. Please check your email or create a new account.";break;case"auth/invalid-email":e="Invalid email address format";break;case"auth/too-many-requests":e="Too many reset attempts. Please wait a few minutes before trying again.";break;case"auth/network-request-failed":e="Network error. Please check your internet connection and try again.";break;default:e=t.message||"Failed to send reset email"}d.A.fire({icon:"error",title:"Reset Failed",text:e})}finally{p(!1)}};return t?(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center",children:[(0,s.jsx)("div",{className:"spinner"}),(0,s.jsx)("p",{className:"text-white mt-4",children:"Loading..."})]}):(0,s.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(i(),{href:"/",className:"inline-block",children:(0,s.jsx)(n.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Reset Password"}),(0,s.jsx)("p",{className:"text-white/80",children:h?"Check your email for reset instructions":"Enter your email to receive a password reset link"})]}),h?(0,s.jsxs)("div",{className:"glass-card p-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"fas fa-check text-green-400 text-2xl"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Email Sent!"}),(0,s.jsxs)("p",{className:"text-white/80 mb-6",children:["We've sent a password reset link to ",(0,s.jsx)("span",{className:"font-semibold text-blue-400",children:r})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("button",{onClick:()=>{x(!1),a("")},className:"w-full btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-redo mr-2"}),"Send to Different Email"]})})]}):(0,s.jsxs)("form",{onSubmit:f,className:"glass-card p-8 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("i",{className:"fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,s.jsx)("input",{type:"email",id:"email",value:r,onChange:e=>a(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter your email address",disabled:m})]})]}),(0,s.jsx)("button",{type:"submit",disabled:m,className:"w-full btn-primary flex items-center justify-center",children:m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Sending Reset Email..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Reset Email"]})})]}),(0,s.jsxs)("div",{className:"mt-6 text-center space-y-3",children:[(0,s.jsxs)(i(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]}),(0,s.jsxs)("div",{className:"text-white/60",children:["Don't have an account?"," ",(0,s.jsx)(i(),{href:"/register",className:"text-white font-semibold hover:underline",children:"Sign up here"})]})]}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("div",{className:"glass-card p-4",children:[(0,s.jsxs)("h4",{className:"text-white font-semibold mb-2",children:[(0,s.jsx)("i",{className:"fas fa-question-circle mr-2"}),"Need Help?"]}),(0,s.jsx)("p",{className:"text-white/60 text-sm mb-3",children:"If you don't receive the email within a few minutes:"}),(0,s.jsxs)("ul",{className:"text-white/60 text-sm space-y-1 text-left",children:[(0,s.jsx)("li",{children:"• Check your spam/junk folder"}),(0,s.jsx)("li",{children:"• Make sure you entered the correct email"}),(0,s.jsx)("li",{children:"• Wait a few minutes and try again"}),(0,s.jsx)("li",{children:"• Contact support if the problem persists"})]})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13612:(e,t,r)=>{Promise.resolve().then(r.bind(r,36200))},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36200)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),o=r(63385),a=r(75535),i=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,o.xI)(n),c=(0,a.aU)(n);(0,i.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},36200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\forgot-password\\page.tsx","default")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{G9:()=>u,M4:()=>c,_f:()=>l,g4:()=>a});var s=r(33784),o=r(77567);function a(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,o=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(`backup_timestamp_${e}`);if(!a)return!1;if(new Date(parseInt(a)).toDateString()!==t)return i(e),!1;let n=localStorage.getItem(`backup_${r}`),l=localStorage.getItem(`backup_${s}`),c=localStorage.getItem(`backup_${o}`),u=!1;if(n&&(localStorage.setItem(r,n),u=!0),l&&(localStorage.setItem(s,l),u=!0),c&&(localStorage.setItem(o,c),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,o=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(r),i=localStorage.getItem(s),n=localStorage.getItem(o);a&&localStorage.setItem(`backup_${r}`,a),i&&localStorage.setItem(`backup_${s}`,i),n&&localStorage.setItem(`backup_${o}`,n),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:a,watchTimesCount:i?JSON.parse(i).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,t="/login"){try{if((await o.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await s.j2.signOut(),o.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e,t="/login",r=!0){try{e&&n(e,r),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let r=localStorage.getItem(e);r&&new Date(parseInt(r)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>i,hD:()=>a,wC:()=>n});var s=r(43210);r(63385),r(33784);var o=r(51278);function a(){let[e,t]=(0,s.useState)(null),[r,a]=(0,s.useState)(!0),i=async()=>{try{await (0,o.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:i}}function i(){let{user:e,loading:t}=a();return{user:e,loading:t}}function n(){let{user:e,loading:t}=a(),[r,o]=(0,s.useState)(!1),[i,n]=(0,s.useState)(!0);return{user:e,loading:t||i,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441],()=>r(32453));module.exports=s})();