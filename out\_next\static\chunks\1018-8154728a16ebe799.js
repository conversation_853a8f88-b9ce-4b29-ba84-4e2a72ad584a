"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1018],{12:(e,t,a)=>{a.d(t,{G9:()=>d,M4:()=>u,_f:()=>l,g4:()=>n});var o=a(6104),r=a(4752),s=a.n(r);function n(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem("backup_timestamp_".concat(e));if(!s)return!1;if(new Date(parseInt(s)).toDateString()!==t)return c(e),!1;let n=localStorage.getItem("backup_".concat(a)),i=localStorage.getItem("backup_".concat(o)),l=localStorage.getItem("backup_".concat(r)),u=!1;if(n&&(localStorage.setItem(a,n),u=!0),i&&(localStorage.setItem(o,i),u=!0),l&&(localStorage.setItem(r,l),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:i?JSON.parse(i).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),c(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function c(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function i(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem(a),n=localStorage.getItem(o),c=localStorage.getItem(r);s&&localStorage.setItem("backup_".concat(a),s),n&&localStorage.setItem("backup_".concat(o),n),c&&localStorage.setItem("backup_".concat(r),c),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:s,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e,!1),await o.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&i(e,a),await o.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},6104:(e,t,a)=>{a.d(t,{db:()=>l,j2:()=>i});var o=a(3915),r=a(3004),s=a(5317),n=a(858);let c=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),i=(0,r.xI)(c),l=(0,s.aU)(c);(0,n.c7)(c)},6681:(e,t,a)=>{a.d(t,{Nu:()=>i,hD:()=>c,wC:()=>l});var o=a(2115),r=a(3004),s=a(6104),n=a(12);function c(){let[e,t]=(0,o.useState)(null),[a,c]=(0,o.useState)(!0);(0,o.useEffect)(()=>{try{let e=(0,r.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),c(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),c(!1)}},[]);let i=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:i}}function i(){let{user:e,loading:t}=c();return(0,o.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=c(),[a,r]=(0,o.useState)(!1),[s,n]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:a}}},7460:(e,t,a)=>{a.d(t,{J:()=>s});var o=a(2115),r=a(3592);function s(e){let[t,a]=(0,o.useState)(!1),[s,n]=(0,o.useState)(!0);(0,o.useEffect)(()=>{e?c():n(!1)},[e]);let c=async()=>{try{n(!0);let t=await (0,r.iA)(e);a(t)}catch(e){console.error("Error checking for blocking notifications:",e),a(!1)}finally{n(!1)}};return{hasBlockingNotifications:t,isChecking:s,checkForBlockingNotifications:c,markAllAsRead:()=>{a(!1)}}}},8647:(e,t,a)=>{a.d(t,{A:()=>n});var o=a(5155),r=a(2115),s=a(3592);function n(e){let{userId:t,onAllRead:a}=e,[n,c]=(0,r.useState)([]),[i,l]=(0,r.useState)(0),[u,d]=(0,r.useState)(!0);(0,r.useEffect)(()=>{t&&g()},[t]);let g=async()=>{try{d(!0);let e=await (0,s.AX)(t);c(e),0===e.length&&a()}catch(e){console.error("Error loading notifications:",e),a()}finally{d(!1)}},h=async()=>{let e=n[i];(null==e?void 0:e.id)&&(await (0,s.bA)(e.id,t),i<n.length-1?l(i+1):a())};if(u)return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,o.jsx)("div",{className:"bg-white rounded-lg p-8 max-w-md w-full mx-4",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Loading notifications..."})]})})});if(0===n.length)return null;let m=n[i];return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("i",{className:(e=>{switch(e){case"success":return"fas fa-check-circle text-green-500";case"warning":return"fas fa-exclamation-triangle text-yellow-500";case"error":return"fas fa-times-circle text-red-500";default:return"fas fa-info-circle text-blue-500"}})(m.type)}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold",children:"Important Notice"}),(0,o.jsxs)("p",{className:"text-blue-100 text-sm",children:[i+1," of ",n.length," notifications"]})]})]}),(0,o.jsx)("div",{className:"bg-white bg-opacity-20 rounded-full px-3 py-1",children:(0,o.jsx)("span",{className:"text-sm font-medium",children:"Required"})})]})}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:m.title}),(0,o.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:(0,o.jsx)("p",{className:"text-gray-800 leading-relaxed",children:m.message})}),(0,o.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-6",children:[(0,o.jsxs)("span",{children:["From: ",m.createdBy]}),(0,o.jsx)("span",{children:(e=>{let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60)," minutes ago"):t<86400?"".concat(Math.floor(t/3600)," hours ago"):"".concat(Math.floor(t/86400)," days ago")})(m.createdAt)})]}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[(0,o.jsx)("span",{children:"Progress"}),(0,o.jsxs)("span",{children:[i+1,"/",n.length]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat((i+1)/n.length*100,"%")}})})]}),(0,o.jsxs)("button",{onClick:h,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2",children:[(0,o.jsx)("i",{className:"fas fa-check"}),(0,o.jsx)("span",{children:i<n.length-1?"Acknowledge & Continue":"Acknowledge & Proceed"})]})]}),(0,o.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-t",children:(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-600",children:[(0,o.jsx)("i",{className:"fas fa-info-circle"}),(0,o.jsx)("span",{children:"You must acknowledge all notifications to continue"})]})})]})})}}}]);