(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2116],{12:(e,t,a)=>{"use strict";a.d(t,{G9:()=>u,M4:()=>d,_f:()=>l,g4:()=>i});var r=a(6104),s=a(4752),o=a.n(s);function i(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),s="daily_watch_times_".concat(e,"_").concat(t),o=localStorage.getItem("backup_timestamp_".concat(e));if(!o)return!1;if(new Date(parseInt(o)).toDateString()!==t)return n(e),!1;let i=localStorage.getItem("backup_".concat(a)),c=localStorage.getItem("backup_".concat(r)),l=localStorage.getItem("backup_".concat(s)),d=!1;if(i&&(localStorage.setItem(a,i),d=!0),c&&(localStorage.setItem(r,c),d=!0),l&&(localStorage.setItem(s,l),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:i,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),n(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function n(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),a="video_session_".concat(e,"_").concat(t),r="watch_times_".concat(e,"_").concat(t),s="daily_watch_times_".concat(e,"_").concat(t),o=localStorage.getItem(a),i=localStorage.getItem(r),n=localStorage.getItem(s);o&&localStorage.setItem("backup_".concat(a),o),i&&localStorage.setItem("backup_".concat(r),i),n&&localStorage.setItem("backup_".concat(s),n),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:o,watchTimesCount:i?JSON.parse(i).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await o().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await r.j2.signOut(),o().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,a),await r.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return c},getImageProps:function(){return n}});let r=a(8229),s=a(8883),o=a(3063),i=r._(a(1193));function n(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let c=o.Image},5430:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(5155),s=a(2115),o=a(6874),i=a.n(o),n=a(6766),c=a(3004),l=a(6104),d=a(6681),u=a(4752),m=a.n(u);function h(){let{user:e,loading:t}=(0,d.hD)(),[a,o]=(0,s.useState)(""),[u,h]=(0,s.useState)(""),[g,f]=(0,s.useState)(!1),[p,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{e&&!t&&(["<EMAIL>","<EMAIL>"].includes(e.email||"")?window.location.href="/admin":(l.j2.signOut(),m().fire({icon:"error",title:"Access Denied",text:"You do not have admin privileges"})))},[e,t]);let b=async e=>{if(e.preventDefault(),!a||!u)return void m().fire({icon:"error",title:"Error",text:"Please fill in all fields"});f(!0);try{let e=(await (0,c.x9)(l.j2,a,u)).user;if(!["<EMAIL>","<EMAIL>"].includes(e.email||""))throw await l.j2.signOut(),Error("Access denied. Admin privileges required.")}catch(t){console.error("Admin login error:",t);let e="An error occurred during login";if(t.message.includes("Access denied"))e="Access denied. Admin privileges required.";else switch(t.code){case"auth/user-not-found":e="No admin account found with this email address";break;case"auth/wrong-password":e="Incorrect password";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/user-disabled":e="This admin account has been disabled";break;case"auth/too-many-requests":e="Too many failed attempts. Please try again later";break;default:e=t.message||"Admin login failed"}m().fire({icon:"error",title:"Admin Login Failed",text:e}),h("")}finally{f(!1)}};return t?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"spinner"})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,r.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,r.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Admin Panel"}),(0,r.jsx)("p",{className:"text-white/80",children:"Sign in to access admin dashboard"})]}),(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:[(0,r.jsx)("i",{className:"fas fa-user-shield mr-2"}),"Admin Email"]}),(0,r.jsx)("input",{type:"email",id:"email",value:a,onChange:e=>o(e.target.value),className:"form-input",placeholder:"Enter admin email",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:[(0,r.jsx)("i",{className:"fas fa-lock mr-2"}),"Password"]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:p?"text":"password",id:"password",value:u,onChange:e=>h(e.target.value),className:"form-input pr-12",placeholder:"Enter admin password",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>w(!p),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors",children:(0,r.jsx)("i",{className:"fas ".concat(p?"fa-eye-slash":"fa-eye")})})]})]}),(0,r.jsx)("button",{type:"submit",disabled:g,className:"w-full btn-primary flex items-center justify-center",children:g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Signing in..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-sign-in-alt mr-2"}),"Admin Login"]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-red-500/20 rounded-lg border border-red-500/30",children:(0,r.jsxs)("div",{className:"flex items-center text-red-300",children:[(0,r.jsx)("i",{className:"fas fa-shield-alt mr-2"}),(0,r.jsx)("span",{className:"text-sm",children:"This is a secure admin area. Only authorized personnel can access this panel."})]})}),(0,r.jsx)("div",{className:"mt-8 text-center",children:(0,r.jsxs)(i(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6104:(e,t,a)=>{"use strict";a.d(t,{db:()=>l,j2:()=>c});var r=a(3915),s=a(3004),o=a(5317),i=a(858);let n=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,s.xI)(n),l=(0,o.aU)(n);(0,i.c7)(n)},6681:(e,t,a)=>{"use strict";a.d(t,{Nu:()=>c,hD:()=>n,wC:()=>l});var r=a(2115),s=a(3004),o=a(6104),i=a(12);function n(){let[e,t]=(0,r.useState)(null),[a,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,s.hg)(o.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),n(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),n(!1)}},[]);let c=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:c}}function c(){let{user:e,loading:t}=n();return(0,r.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=n(),[a,s]=(0,r.useState)(!1),[o,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");s(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||o,isAdmin:a}}},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>s.a});var r=a(1469),s=a.n(r)},9833:(e,t,a)=>{Promise.resolve().then(a.bind(a,5430))}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(9833)),_N_E=e.O()}]);