(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6393],{12:(e,t,s)=>{"use strict";s.d(t,{G9:()=>m,M4:()=>d,_f:()=>l,g4:()=>i});var a=s(6104),n=s(4752),r=s.n(n);function i(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),n="daily_watch_times_".concat(e,"_").concat(t),r=localStorage.getItem("backup_timestamp_".concat(e));if(!r)return!1;if(new Date(parseInt(r)).toDateString()!==t)return o(e),!1;let i=localStorage.getItem("backup_".concat(s)),c=localStorage.getItem("backup_".concat(a)),l=localStorage.getItem("backup_".concat(n)),d=!1;if(i&&(localStorage.setItem(s,i),d=!0),c&&(localStorage.setItem(a,c),d=!0),l&&(localStorage.setItem(n,l),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:i,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),o(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function o(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),n="daily_watch_times_".concat(e,"_").concat(t),r=localStorage.getItem(s),i=localStorage.getItem(a),o=localStorage.getItem(n);r&&localStorage.setItem("backup_".concat(s),r),i&&localStorage.setItem("backup_".concat(a),i),o&&localStorage.setItem("backup_".concat(n),o),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:r,watchTimesCount:i?JSON.parse(i).length:0,dailyWatchTimesCount:o?JSON.parse(o).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await r().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await a.j2.signOut(),r().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),r().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,s),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function m(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let s=localStorage.getItem(e);s&&new Date(parseInt(s)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},2719:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5155),n=s(2115),r=s(6874),i=s.n(r),o=s(6681),c=s(3592),l=s(4752),d=s.n(l);function m(){let{user:e,loading:t,isAdmin:s}=(0,o.wC)(),[r,l]=(0,n.useState)(!1),m=async()=>{try{l(!0),await (0,c.z8)({title:"\uD83D\uDEA8 Important System Update",message:"This is a test notification. Users must acknowledge this message before they can continue using the platform. This ensures important announcements are seen by all users.",type:"warning",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users. Users will need to acknowledge this before accessing any features.",timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error sending blocking notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send blocking notification. Please try again."})}finally{l(!1)}},g=async()=>{try{l(!0),await (0,c.z8)({title:"Another Test Notification",message:"This is another test notification. All notifications are now blocking and users must acknowledge them.",type:"info",targetUsers:"all",userIds:[],createdBy:(null==e?void 0:e.email)||"admin"}),d().fire({icon:"success",title:"Notification Sent!",text:"Test notification sent to all users.",timer:3e3,showConfirmButton:!1})}catch(e){console.error("Error sending notification:",e),d().fire({icon:"error",title:"Send Failed",text:"Failed to send notification. Please try again."})}finally{l(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex items-center justify-between px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i(),{href:"/admin",className:"text-gray-500 hover:text-gray-700",children:(0,a.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Test Blocking Notifications"})]})})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[(0,a.jsxs)("h2",{className:"text-lg font-bold text-blue-900 mb-3",children:[(0,a.jsx)("i",{className:"fas fa-info-circle mr-2"}),"How to Test Notifications"]}),(0,a.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"1."})," Send a notification using the buttons below"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"2."})," Open a new tab and go to the user dashboard or work page"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"3."})," You should see a full-page modal that blocks all activities until acknowledged"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"4."}),' Click "Acknowledge" to dismiss the notification']}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"5."})," All notifications are now blocking/mandatory for better user engagement"]})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 text-2xl"})}),(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDEA8 Warning Notification"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends a warning notification that users must acknowledge before continuing"}),(0,a.jsx)("button",{onClick:m,disabled:r,className:"w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Warning Notification"]})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"fas fa-bell text-blue-600 text-2xl"})}),(0,a.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:"\uD83D\uDCE2 Info Notification"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Sends an info notification that users must acknowledge before continuing"}),(0,a.jsx)("button",{onClick:g,disabled:r,className:"w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-4 rounded-lg disabled:opacity-50",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner w-4 h-4 mr-2 inline-block"}),"Sending..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-paper-plane mr-2"}),"Send Info Notification"]})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mt-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2 text-green-500"}),"Notification Features (All Blocking)"]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Blocks all user activities until acknowledged"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-eye text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Forces users to read important announcements"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-users text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Can target all users or specific users"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Works on all pages (dashboard, work, wallet, etc.)"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-chart-line text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Progress indicator for multiple notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-clock text-green-500 mr-3"}),(0,a.jsx)("span",{children:"Persistent until user acknowledges"})]})]})]})]})]})})]})}},4552:(e,t,s)=>{Promise.resolve().then(s.bind(s,2719))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>c});var a=s(3915),n=s(3004),r=s(5317),i=s(858);let o=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,n.xI)(o),l=(0,r.aU)(o);(0,i.c7)(o)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>o,wC:()=>l});var a=s(2115),n=s(3004),r=s(6104),i=s(12);function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,n.hg)(r.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let c=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=o();return(0,a.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=o(),[s,n]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");n(t),i(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||r,isAdmin:s}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>t(4552)),_N_E=e.O()}]);