'use client'

import { useState, useEffect } from 'react'
import { User, onAuthStateChanged } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { quickLogout } from '@/lib/authUtils'

export function useAuthState() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    try {
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        console.log('Auth state changed:', user ? 'User logged in' : 'No user')
        setUser(user)
        setLoading(false)
      })

      return () => unsubscribe()
    } catch (error) {
      console.error('Error in auth state listener:', error)
      setLoading(false)
    }
  }, [])

  const signOut = async () => {
    try {
      await quickLogout(user?.uid, '/')
    } catch (error) {
      console.error('Error signing out:', error)
      // Force redirect on error
      window.location.href = '/'
    }
  }

  return { user, loading, signOut }
}

export function useRequireAuth() {
  const { user, loading } = useAuthState()

  useEffect(() => {
    if (!loading && !user) {
      // Check if this is an auto-logout scenario by looking for session data
      const hasSessionData = checkForActiveSession()

      if (hasSessionData) {
        console.log('🔄 Auto-logout detected with active session data')
        // Redirect to login with a flag to indicate session restoration
        window.location.href = '/login?restore=true'
      } else {
        window.location.href = '/login'
      }
    }
  }, [user, loading])

  return { user, loading }
}

// Helper function to check if user has active session data
function checkForActiveSession(): boolean {
  try {
    const today = new Date().toDateString()
    const keys = Object.keys(localStorage)

    // Look for today's session data
    const hasSessionData = keys.some(key =>
      (key.startsWith('video_session_') || key.startsWith('watch_times_')) &&
      key.includes(today)
    )

    return hasSessionData
  } catch (error) {
    console.error('Error checking for active session:', error)
    return false
  }
}

export function useRequireAdmin() {
  const { user, loading } = useAuthState()
  const [isAdmin, setIsAdmin] = useState(false)
  const [adminLoading, setAdminLoading] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      window.location.href = '/admin/login'
      return
    }

    if (user) {
      // Check if user is admin
      // This would typically involve checking a custom claim or database
      // For now, we'll use a simple email check
      const adminEmails = ['<EMAIL>', '<EMAIL>']
      const userIsAdmin = adminEmails.includes(user.email || '')
      setIsAdmin(userIsAdmin)
      setAdminLoading(false)

      if (!userIsAdmin) {
        window.location.href = '/login'
      }
    }
  }, [user, loading])

  return { user, loading: loading || adminLoading, isAdmin }
}
