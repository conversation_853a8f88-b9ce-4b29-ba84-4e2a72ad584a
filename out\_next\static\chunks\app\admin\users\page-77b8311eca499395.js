(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6779,8733],{2899:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>g});var s=t(5155),r=t(2115),i=t(6874),n=t.n(i),l=t(6681),o=t(6779),d=t(3592),c=t(3737),u=t(4752),x=t.n(u);function g(){let{user:e,loading:a,isAdmin:t}=(0,l.wC)(),[i,u]=(0,r.useState)([]),[g,m]=(0,r.useState)(!0),[h,p]=(0,r.useState)(""),[y,v]=(0,r.useState)(!1),[f,b]=(0,r.useState)(0),[w,j]=(0,r.useState)(null),[N,D]=(0,r.useState)(!1),[E,k]=(0,r.useState)({name:"",email:"",mobile:"",referralCode:"",referredBy:"",plan:"",activeDays:0,totalVideos:0,todayVideos:0,wallet:0,status:"active",videoDuration:300,quickVideoAdvantage:!1,quickVideoAdvantageDays:7,quickVideoAdvantageSeconds:30}),[A,C]=(0,r.useState)(!1),[S,V]=(0,r.useState)(1),[L,q]=(0,r.useState)(!0),[I,M]=(0,r.useState)(null);(0,r.useEffect)(()=>{t&&F()},[t]);let F=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];try{m(!0);let a=await (0,o.lo)(50,e?null:I);if(e){u(a.users),V(1);try{let e=await (0,o.nQ)();b(e)}catch(e){console.error("Error getting total user count:",e)}}else u(e=>[...e,...a.users]);M(a.lastDoc),q(a.hasMore)}catch(e){console.error("Error loading users:",e),x().fire({icon:"error",title:"Error",text:"Failed to load users. Please try again."})}finally{m(!1)}},O=async()=>{if(!h.trim())return void F();try{v(!0);let e=await (0,o.x5)(h.trim());u(e),q(!1)}catch(e){console.error("Error searching users:",e),x().fire({icon:"error",title:"Search Failed",text:"Failed to search users. Please try again."})}finally{v(!1)}},_=e=>{j(e),k({name:e.name,email:e.email,mobile:e.mobile,referralCode:e.referralCode,referredBy:e.referredBy,plan:e.plan,activeDays:e.activeDays,totalVideos:e.totalVideos,todayVideos:e.todayVideos,wallet:e.wallet||0,status:e.status,videoDuration:e.videoDuration||300,quickVideoAdvantage:e.quickVideoAdvantage||!1,quickVideoAdvantageDays:e.quickVideoAdvantageDays||7,quickVideoAdvantageSeconds:e.quickVideoAdvantageSeconds||30}),D(!0)},T=async()=>{if(w)try{C(!0);let a=w.plan,t=E.plan,s=a!==t,r=E.activeDays!==w.activeDays,i={name:E.name,email:E.email,mobile:E.mobile,referralCode:E.referralCode,referredBy:E.referredBy,plan:E.plan,activeDays:E.activeDays,totalVideos:E.totalVideos,todayVideos:E.todayVideos,wallet:E.wallet,status:E.status};r&&(i.manuallySetActiveDays=!0),await (0,o.TK)(w.id,i),E.videoDuration!==(w.videoDuration||300)&&await (0,d.Gl)(w.id,E.videoDuration);let n=!!w.quickVideoAdvantage;if(E.quickVideoAdvantage&&!n?await (0,d.w1)(w.id,E.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",E.quickVideoAdvantageSeconds):!E.quickVideoAdvantage&&n?await (0,d.wT)(w.id,(null==e?void 0:e.email)||"admin"):E.quickVideoAdvantage&&n&&(await (0,d.wT)(w.id,(null==e?void 0:e.email)||"admin"),await (0,d.w1)(w.id,E.quickVideoAdvantageDays,(null==e?void 0:e.email)||"admin",E.quickVideoAdvantageSeconds)),s)try{await (0,d.II)(w.id,t),console.log("Updated plan expiry for user ".concat(w.id,": ").concat(a," -> ").concat(t))}catch(e){console.error("Error updating plan expiry:",e)}if(s&&"Trial"===a&&"Trial"!==t)try{console.log("Processing referral bonus for user ".concat(w.id,": ").concat(a," -> ").concat(t)),await (0,d.IK)(w.id,a,t),x().fire({icon:"success",title:"User Updated & Referral Bonus Processed",html:'\n              <div class="text-left">\n                <p><strong>User plan updated:</strong> '.concat(a," → ").concat(t,"</p>\n                <p><strong>Referral bonus:</strong> Processed for referrer (if applicable)</p>\n              </div>\n            "),timer:4e3,showConfirmButton:!1})}catch(e){console.error("Error processing referral bonus:",e),x().fire({icon:"warning",title:"User Updated (Referral Bonus Issue)",html:'\n              <div class="text-left">\n                <p><strong>User plan updated successfully:</strong> '.concat(a," → ").concat(t,'</p>\n                <p><strong>Referral bonus:</strong> Could not be processed automatically</p>\n                <p class="text-sm text-gray-600 mt-2">Please check referral bonus manually if needed.</p>\n              </div>\n            '),timer:5e3,showConfirmButton:!1})}else{let e="User information has been updated successfully";E.quickVideoAdvantage&&!n?e+=". Quick video advantage granted for ".concat(E.quickVideoAdvantageDays," days."):!E.quickVideoAdvantage&&n?e+=". Quick video advantage removed.":E.quickVideoAdvantage&&n&&(e+=". Quick video advantage updated for ".concat(E.quickVideoAdvantageDays," days.")),x().fire({icon:"success",title:"User Updated",text:e,timer:3e3,showConfirmButton:!1})}u(e=>e.map(e=>e.id===w.id?{...e,...i,videoDuration:E.videoDuration,quickVideoAdvantage:E.quickVideoAdvantage,quickVideoAdvantageDays:E.quickVideoAdvantage?E.quickVideoAdvantageDays:0,quickVideoAdvantageSeconds:E.quickVideoAdvantage?E.quickVideoAdvantageSeconds:30,quickVideoAdvantageExpiry:E.quickVideoAdvantage?new Date(Date.now()+24*E.quickVideoAdvantageDays*36e5):null}:e)),D(!1),j(null),await F()}catch(e){console.error("Error updating user:",e),x().fire({icon:"error",title:"Update Failed",text:"Failed to update user. Please try again."})}finally{C(!1)}},B=async e=>{if((await x().fire({icon:"warning",title:"Delete User",text:"Are you sure you want to delete ".concat(e.name,"? This action cannot be undone."),showCancelButton:!0,confirmButtonText:"Yes, Delete",confirmButtonColor:"#dc2626",cancelButtonText:"Cancel"})).isConfirmed)try{await (0,o.hG)(e.id),u(a=>a.filter(a=>a.id!==e.id)),x().fire({icon:"success",title:"User Deleted",text:"User has been deleted successfully",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting user:",e),x().fire({icon:"error",title:"Delete Failed",text:"Failed to delete user. Please try again."})}},P=e=>null==e||isNaN(e)?"₹0.00":"₹".concat(e.toFixed(2)),U=e=>{switch(e){case"Trial":default:return"bg-gray-500";case"Starter":return"bg-blue-500";case"Basic":return"bg-green-500";case"Premium":return"bg-purple-500";case"Gold":return"bg-yellow-500";case"Platinum":return"bg-indigo-500";case"Diamond":return"bg-pink-500"}},R=e=>{switch(e){case"active":return"bg-green-500";case"inactive":return"bg-red-500";case"suspended":return"bg-yellow-500";default:return"bg-gray-500"}},Q=async()=>{try{x().fire({title:"Exporting Users...",text:"Please wait while we prepare your export file.",allowOutsideClick:!1,didOpen:()=>{x().showLoading()}});let e=await (0,o.CF)();if(0===e.length)return void x().fire({icon:"warning",title:"No Data",text:"No users to export."});let a=(0,c.Fz)(e);(0,c.Bf)(a,"users"),x().fire({icon:"success",title:"Export Complete",text:"Exported ".concat(e.length," users to CSV file."),timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error exporting users:",e),x().fire({icon:"error",title:"Export Failed",text:"Failed to export users. Please try again."})}};return a?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,s.jsxs)(n(),{href:"/admin",className:"text-gray-600 hover:text-gray-800",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Dashboard"]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),f>0&&(0,s.jsx)("p",{className:"text-sm text-gray-600",children:h?"Showing ".concat(i.length," of ").concat(f," users"):"Total: ".concat(f," users")})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n(),{href:"/admin/upload-users",className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 inline-flex items-center",children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]}),(0,s.jsxs)("button",{onClick:Q,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Export CSV"]}),(0,s.jsxs)("button",{onClick:()=>F(),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Refresh"]})]})]})}),(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("input",{type:"text",value:h,onChange:e=>p(e.target.value),placeholder:"Search by name, email, mobile, or referral code...",className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyDown:e=>"Enter"===e.key&&O()}),(0,s.jsx)("button",{onClick:O,disabled:y,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Searching..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-search mr-2"}),"Search"]})}),h&&(0,s.jsx)("button",{onClick:()=>{p(""),F()},className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:(0,s.jsx)("i",{className:"fas fa-times"})})]})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plan"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Videos"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Duration"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Quick Advantage"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Wallet"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g&&0===i.length?(0,s.jsx)("tr",{children:(0,s.jsxs)("td",{colSpan:9,className:"px-6 py-4 text-center",children:[(0,s.jsx)("div",{className:"spinner mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading users..."})]})}):0===i.length?(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:9,className:"px-6 py-4 text-center text-gray-500",children:"No users found"})}):i.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Joined: ",e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():new Date(e.joinedDate).toLocaleDateString()]})]})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:e.mobile}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Code: ",e.referralCode]}),e.referredBy&&(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Ref: ",e.referredBy]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(U(e.plan)),children:e.plan}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 mt-1",children:["Days: ",e.activeDays]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:["Total: ",e.totalVideos]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Today: ",e.todayVideos]})]}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm text-gray-900",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300,"s"):"".concat(Math.round((e.videoDuration||300)/60),"m")}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:60>(e.videoDuration||300)?"".concat(e.videoDuration||300," second").concat((e.videoDuration||300)>1?"s":""):"".concat(Math.round((e.videoDuration||300)/60)," minute").concat(Math.round((e.videoDuration||300)/60)>1?"s":"")})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:e.quickVideoAdvantage&&(e.quickVideoAdvantageRemainingDays&&e.quickVideoAdvantageRemainingDays>0||e.quickVideoAdvantageExpiry&&new Date<e.quickVideoAdvantageExpiry)?(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-green-500",children:"Active"}),(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:void 0!==e.quickVideoAdvantageRemainingDays?"".concat(e.quickVideoAdvantageRemainingDays," days left"):e.quickVideoAdvantageExpiry?"Until: ".concat(e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():new Date(e.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"})]}):(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white bg-gray-500",children:"None"})}),(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-900",children:[(0,s.jsx)("i",{className:"fas fa-wallet mr-1 text-green-500"}),P(e.wallet||0)]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"Total Balance"})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full text-white ".concat(R(e.status)),children:e.status})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>_(e),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:(0,s.jsx)("i",{className:"fas fa-edit"})}),(0,s.jsx)("button",{onClick:()=>B(e),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,s.jsx)("i",{className:"fas fa-trash"})})]})})]},e.id))})]})}),L&&!g&&i.length>0&&(0,s.jsx)("div",{className:"px-6 py-4 border-t border-gray-200 text-center",children:(0,s.jsxs)("button",{onClick:()=>{L&&!g&&F(!1)},className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:[(0,s.jsx)("i",{className:"fas fa-chevron-down mr-2"}),"Load More Users"]})})]})}),N&&w&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center z-50 p-4 overflow-y-auto modal-scrollable",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg w-full max-w-md my-8 max-h-[calc(100vh-2rem)] flex flex-col shadow-xl",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-200 flex-shrink-0",children:(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Edit User"})}),(0,s.jsxs)("div",{className:"p-6 space-y-4 flex-1 overflow-y-auto modal-scrollable",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),(0,s.jsx)("input",{type:"text",value:E.name,onChange:e=>k(a=>({...a,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,s.jsx)("input",{type:"email",value:E.email,onChange:e=>k(a=>({...a,email:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Mobile"}),(0,s.jsx)("input",{type:"text",value:E.mobile,onChange:e=>k(a=>({...a,mobile:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referral Code"}),(0,s.jsx)("input",{type:"text",value:E.referralCode,onChange:e=>k(a=>({...a,referralCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Referred By"}),(0,s.jsx)("input",{type:"text",value:E.referredBy,onChange:e=>k(a=>({...a,referredBy:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,s.jsxs)("select",{value:E.plan,onChange:e=>k(a=>({...a,plan:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"Trial",children:"Trial"}),(0,s.jsx)("option",{value:"Starter",children:"Starter"}),(0,s.jsx)("option",{value:"Basic",children:"Basic"}),(0,s.jsx)("option",{value:"Premium",children:"Premium"}),(0,s.jsx)("option",{value:"Gold",children:"Gold"}),(0,s.jsx)("option",{value:"Platinum",children:"Platinum"}),(0,s.jsx)("option",{value:"Diamond",children:"Diamond"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Active Days"}),(0,s.jsx)("input",{type:"number",value:E.activeDays,onChange:e=>k(a=>({...a,activeDays:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Total Videos"}),(0,s.jsx)("input",{type:"number",value:E.totalVideos,onChange:e=>k(a=>({...a,totalVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Today Videos"}),(0,s.jsx)("input",{type:"number",value:E.todayVideos,onChange:e=>k(a=>({...a,todayVideos:parseInt(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Wallet Balance (₹)"}),(0,s.jsx)("input",{type:"number",step:"0.01",value:E.wallet,onChange:e=>k(a=>({...a,wallet:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,s.jsxs)("select",{value:E.videoDuration,onChange:e=>k(a=>({...a,videoDuration:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsxs)("optgroup",{label:"\uD83D\uDE80 Quick Duration",children:[(0,s.jsx)("option",{value:1,children:"1 second"}),(0,s.jsx)("option",{value:10,children:"10 seconds"}),(0,s.jsx)("option",{value:30,children:"30 seconds"})]}),(0,s.jsxs)("optgroup",{label:"⏱️ Standard Duration",children:[(0,s.jsx)("option",{value:60,children:"1 minute"}),(0,s.jsx)("option",{value:120,children:"2 minutes"}),(0,s.jsx)("option",{value:180,children:"3 minutes"}),(0,s.jsx)("option",{value:240,children:"4 minutes"}),(0,s.jsx)("option",{value:300,children:"5 minutes"}),(0,s.jsx)("option",{value:360,children:"6 minutes"}),(0,s.jsx)("option",{value:420,children:"7 minutes"})]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:E.videoDuration<60?"".concat(E.videoDuration," second").concat(E.videoDuration>1?"s":""):"".concat(Math.round(E.videoDuration/60)," minute").concat(Math.round(E.videoDuration/60)>1?"s":"")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,s.jsxs)("select",{value:E.status,onChange:e=>k(a=>({...a,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"}),(0,s.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsxs)("h4",{className:"text-md font-semibold text-gray-900 mb-3",children:[(0,s.jsx)("i",{className:"fas fa-bolt mr-2 text-yellow-500"}),"Quick Video Advantage"]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"quickVideoAdvantage",checked:E.quickVideoAdvantage,onChange:e=>k(a=>({...a,quickVideoAdvantage:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"quickVideoAdvantage",className:"ml-2 block text-sm text-gray-700",children:"Grant Quick Video Advantage"})]}),E.quickVideoAdvantage&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 ml-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Days"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:E.quickVideoAdvantageDays,onChange:e=>k(a=>({...a,quickVideoAdvantageDays:parseInt(e.target.value)||7})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Video Duration"}),(0,s.jsxs)("select",{value:E.quickVideoAdvantageSeconds,onChange:e=>k(a=>({...a,quickVideoAdvantageSeconds:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:1,children:"1 second"}),(0,s.jsx)("option",{value:10,children:"10 seconds"}),(0,s.jsx)("option",{value:30,children:"30 seconds"})]})]})]}),w&&(0,s.jsx)("div",{className:"ml-6 p-3 bg-gray-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,s.jsx)("strong",{children:"Current Status:"})," ",w.quickVideoAdvantage&&(w.quickVideoAdvantageRemainingDays&&w.quickVideoAdvantageRemainingDays>0||w.quickVideoAdvantageExpiry&&new Date<w.quickVideoAdvantageExpiry)?(0,s.jsx)("span",{className:"text-green-600",children:void 0!==w.quickVideoAdvantageRemainingDays?"Active - ".concat(w.quickVideoAdvantageRemainingDays," days remaining"):w.quickVideoAdvantageExpiry?"Active until ".concat(w.quickVideoAdvantageExpiry instanceof Date?w.quickVideoAdvantageExpiry.toLocaleDateString():new Date(w.quickVideoAdvantageExpiry).toLocaleDateString()):"Active"}):(0,s.jsx)("span",{className:"text-gray-500",children:"Not active"})]})})]})]})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-200 flex-shrink-0",children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("button",{onClick:T,disabled:A,className:"flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50",children:A?"Saving...":"Save Changes"}),(0,s.jsx)("button",{onClick:()=>D(!1),className:"flex-1 bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700",children:"Cancel"})]})})]})})]})}},6779:(e,a,t)=>{"use strict";t.d(a,{CF:()=>c,I0:()=>x,Pn:()=>l,TK:()=>m,getWithdrawals:()=>g,hG:()=>h,lo:()=>o,nQ:()=>u,updateWithdrawalStatus:()=>p,x5:()=>d});var s=t(5317),r=t(6104),i=t(3592);let n=new Map;async function l(){let e="dashboard-stats",a=function(e){let a=n.get(e);return a&&Date.now()-a.timestamp<3e5?a.data:null}(e);if(a)return a;try{let a=new Date;a.setHours(0,0,0,0);let t=s.Dc.fromDate(a),l=await (0,s.getDocs)((0,s.collection)(r.db,i.COLLECTIONS.users)),o=l.size,d=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users),(0,s._M)(i.FIELD_NAMES.joinedDate,">=",t)),c=(await (0,s.getDocs)(d)).size,u=0,x=0,g=0,m=0;l.forEach(e=>{var t;let s=e.data();u+=s[i.FIELD_NAMES.totalVideos]||0,x+=s[i.FIELD_NAMES.wallet]||0;let r=null==(t=s[i.FIELD_NAMES.lastVideoDate])?void 0:t.toDate();r&&r.toDateString()===a.toDateString()&&(g+=s[i.FIELD_NAMES.todayVideos]||0)});try{let e=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.transactions),(0,s._M)(i.FIELD_NAMES.type,"==","video_earning"),(0,s.AB)(1e3));(await (0,s.getDocs)(e)).forEach(e=>{var t;let s=e.data(),r=null==(t=s[i.FIELD_NAMES.date])?void 0:t.toDate();r&&r>=a&&(m+=s[i.FIELD_NAMES.amount]||0)})}catch(e){console.warn("Could not fetch today's transactions:",e)}let h=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.withdrawals),(0,s._M)("status","==","pending")),p=(await (0,s.getDocs)(h)).size,y=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.withdrawals),(0,s._M)("date",">=",t)),v=(await (0,s.getDocs)(y)).size,f={totalUsers:o,totalVideos:u,totalEarnings:x,pendingWithdrawals:p,todayUsers:c,todayVideos:g,todayEarnings:m,todayWithdrawals:v};return n.set(e,{data:f,timestamp:Date.now()}),f}catch(e){throw console.error("Error getting admin dashboard stats:",e),e}}async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,s.AB)(e));a&&(t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"),(0,s.HM)(a),(0,s.AB)(e)));let n=await (0,s.getDocs)(t);return{users:n.docs.map(e=>{var a,t;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[i.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(t=e.data()[i.FIELD_NAMES.planExpiry])?void 0:t.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting users:",e),e}}async function d(e){try{if(!e||0===e.trim().length)return[];let a=e.toLowerCase().trim(),t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(t)).docs.map(e=>{var a,t;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[i.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(t=e.data()[i.FIELD_NAMES.planExpiry])?void 0:t.toDate()}}).filter(e=>{let t=String(e[i.FIELD_NAMES.name]||"").toLowerCase(),s=String(e[i.FIELD_NAMES.email]||"").toLowerCase(),r=String(e[i.FIELD_NAMES.mobile]||"").toLowerCase(),n=String(e[i.FIELD_NAMES.referralCode]||"").toLowerCase();return t.includes(a)||s.includes(a)||r.includes(a)||n.includes(a)})}catch(e){throw console.error("Error searching users:",e),e}}async function c(){try{let e=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users),(0,s.My)(i.FIELD_NAMES.joinedDate,"desc"));return(await (0,s.getDocs)(e)).docs.map(e=>{var a,t;return{id:e.id,...e.data(),joinedDate:null==(a=e.data()[i.FIELD_NAMES.joinedDate])?void 0:a.toDate(),planExpiry:null==(t=e.data()[i.FIELD_NAMES.planExpiry])?void 0:t.toDate()}})}catch(e){throw console.error("Error getting all users:",e),e}}async function u(){try{let e=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.users));return(await (0,s.getDocs)(e)).size}catch(e){throw console.error("Error getting total user count:",e),e}}async function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.transactions),(0,s.My)(i.FIELD_NAMES.date,"desc"),(0,s.AB)(e));a&&(t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.transactions),(0,s.My)(i.FIELD_NAMES.date,"desc"),(0,s.HM)(a),(0,s.AB)(e)));let n=await (0,s.getDocs)(t);return{transactions:n.docs.map(e=>{var a;return{id:e.id,...e.data(),date:null==(a=e.data()[i.FIELD_NAMES.date])?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting transactions:",e),e}}async function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;try{let t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.AB)(e));a&&(t=(0,s.P)((0,s.collection)(r.db,i.COLLECTIONS.withdrawals),(0,s.My)("date","desc"),(0,s.HM)(a),(0,s.AB)(e)));let n=await (0,s.getDocs)(t);return{withdrawals:n.docs.map(e=>{var a;return{id:e.id,...e.data(),date:null==(a=e.data().date)?void 0:a.toDate()}}),lastDoc:n.docs[n.docs.length-1]||null,hasMore:n.docs.length===e}}catch(e){throw console.error("Error getting withdrawals:",e),e}}async function m(e,a){try{await (0,s.mZ)((0,s.H9)(r.db,i.COLLECTIONS.users,e),a),n.delete("dashboard-stats")}catch(e){throw console.error("Error updating user:",e),e}}async function h(e){try{await (0,s.kd)((0,s.H9)(r.db,i.COLLECTIONS.users,e)),n.delete("dashboard-stats")}catch(e){throw console.error("Error deleting user:",e),e}}async function p(e,a,l){try{let o=await (0,s.x7)((0,s.H9)(r.db,i.COLLECTIONS.withdrawals,e));if(!o.exists())throw Error("Withdrawal not found");let{userId:d,amount:c,status:u}=o.data(),x={status:a,updatedAt:s.Dc.now()};if(l&&(x.adminNotes=l),await (0,s.mZ)((0,s.H9)(r.db,i.COLLECTIONS.withdrawals,e),x),"approved"===a&&"approved"!==u){let{addTransaction:e}=await Promise.resolve().then(t.bind(t,3592));await e(d,{type:"withdrawal_approved",amount:0,description:"Withdrawal approved - ₹".concat(c," processed for transfer")})}if("rejected"===a&&"rejected"!==u){let{updateWalletBalance:e,addTransaction:a}=await Promise.resolve().then(t.bind(t,3592));await e(d,c),await a(d,{type:"withdrawal_rejected",amount:c,description:"Withdrawal rejected - ₹".concat(c," credited back to wallet")})}n.delete("dashboard-stats")}catch(e){throw console.error("Error updating withdrawal status:",e),e}}},7428:(e,a,t)=>{Promise.resolve().then(t.bind(t,2899))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3592,6465,8441,1684,7358],()=>a(7428)),_N_E=e.O()}]);