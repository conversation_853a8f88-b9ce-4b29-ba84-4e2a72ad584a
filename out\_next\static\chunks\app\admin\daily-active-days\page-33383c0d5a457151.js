(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3179],{12:(e,s,t)=>{"use strict";t.d(s,{M4:()=>o,_f:()=>l});var r=t(6104),n=t(4752),a=t.n(n);function i(e){try{Object.keys(localStorage).forEach(s=>{(s.includes(e)||s.startsWith("video_session_")||s.startsWith("watch_times_")||s.startsWith("video_refresh_")||s.startsWith("video_change_notification_")||s.startsWith("leave_")||s.includes("mytube_")||s.includes("user_"))&&localStorage.removeItem(s)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await a().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&i(e),await r.j2.signOut(),a().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=s}),!0;return!1}catch(e){return console.error("Logout error:",e),a().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{e&&i(e),await r.j2.signOut(),window.location.href=s}catch(e){console.error("Quick logout error:",e),window.location.href=s}}},3633:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var r=t(5155),n=t(2115),a=t(6874),i=t.n(a),l=t(6681),o=t(3592),c=t(4752),d=t.n(c);function u(){let{user:e,loading:s,isAdmin:t}=(0,l.wC)(),[a,c]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null);if(s)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})});if(!t)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})});let h=async()=>{try{c(!0);let e=await (0,o.Oe)();m(e),d().fire({icon:"success",title:"Daily Active Days Increment Completed!",html:'\n          <div class="text-left">\n            <p><strong>Incremented:</strong> '.concat(e.incrementedCount," users</p>\n            <p><strong>Skipped:</strong> ").concat(e.skippedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n            ").concat(e.reason?"<p><strong>Reason:</strong> ".concat(e.reason,"</p>"):"","\n          </div>\n        "),timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running daily active days increment:",e),d().fire({icon:"error",title:"Error",text:"Failed to run daily active days increment. Please try again."})}finally{c(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Daily Active Days Management"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Manually trigger daily active days increment for all users"})]}),(0,r.jsxs)(i(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,r.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin"]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,r.jsx)("i",{className:"fas fa-calendar-plus mr-2 text-blue-500"}),"Daily Active Days Increment"]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"How it works:"}),(0,r.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,r.jsx)("li",{children:"• Increments active days by 1 for all users (regardless of login status)"}),(0,r.jsx)("li",{children:"• Skips users who are on approved leave today"}),(0,r.jsx)("li",{children:"• Skips increment if today is an admin leave day"}),(0,r.jsx)("li",{children:"• Only processes each user once per day"}),(0,r.jsx)("li",{children:"• Preserves manually set active days by admin"})]})]}),(0,r.jsx)("button",{onClick:h,disabled:a,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"fas fa-play mr-2"}),"Run Daily Active Days Increment"]})})]}),u&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Last Execution Result"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.incrementedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Incremented"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:u.skippedCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:u.errorCount}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]}),u.reason&&(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm",children:u.reason})})]})]}),(0,r.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Important Notes"}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("ul",{className:"text-yellow-800 text-sm space-y-2",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Automatic Execution:"})," This process also runs automatically when users interact with the platform (once per day)"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Manual Trigger:"})," Use this page to manually trigger the process if needed"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Safety:"})," The process is safe to run multiple times per day - it will skip users already processed"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Leave Days:"})," Active days will not increment on admin leave days or user leave days"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Manual Override:"})," Users with manually set active days will still get daily increments"]})]})})]})]})]})})}},6104:(e,s,t)=>{"use strict";t.d(s,{db:()=>c,j2:()=>o});var r=t(3915),n=t(3004),a=t(5317),i=t(858);let l=(0,r.Dk)().length?(0,r.Sx)():(0,r.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),o=(0,n.xI)(l),c=(0,a.aU)(l);(0,i.c7)(l)},6681:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>o,hD:()=>l,wC:()=>c});var r=t(2115),n=t(3004),a=t(6104),i=t(12);function l(){let[e,s]=(0,r.useState)(null),[t,l]=(0,r.useState)(!0);(0,r.useEffect)(()=>{try{let e=(0,n.hg)(a.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),s(e),l(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),l(!1)}},[]);let o=async()=>{try{await (0,i.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:o}}function o(){let{user:e,loading:s}=l();return(0,r.useEffect)(()=>{s||e||(window.location.href="/login")},[e,s]),{user:e,loading:s}}function c(){let{user:e,loading:s}=l(),[t,n]=(0,r.useState)(!1),[a,i]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{if(!s&&!e){window.location.href="/admin/login";return}if(e){let s=["<EMAIL>","<EMAIL>"].includes(e.email||"");n(s),i(!1),s||(window.location.href="/login")}},[e,s]),{user:e,loading:s||a,isAdmin:t}}},9178:(e,s,t)=>{Promise.resolve().then(t.bind(t,3633))}},e=>{var s=s=>e(e.s=s);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>s(9178)),_N_E=e.O()}]);