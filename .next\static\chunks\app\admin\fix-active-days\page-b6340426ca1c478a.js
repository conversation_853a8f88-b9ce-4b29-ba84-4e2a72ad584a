(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1561],{12:(e,t,s)=>{"use strict";s.d(t,{G9:()=>u,M4:()=>d,_f:()=>l,g4:()=>n});var a=s(6104),r=s(4752),o=s.n(r);function n(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),o=localStorage.getItem("backup_timestamp_".concat(e));if(!o)return!1;if(new Date(parseInt(o)).toDateString()!==t)return i(e),!1;let n=localStorage.getItem("backup_".concat(s)),c=localStorage.getItem("backup_".concat(a)),l=localStorage.getItem("backup_".concat(r)),d=!1;if(n&&(localStorage.setItem(s,n),d=!0),c&&(localStorage.setItem(a,c),d=!0),l&&(localStorage.setItem(r,l),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),r="daily_watch_times_".concat(e,"_").concat(t),o=localStorage.getItem(s),n=localStorage.getItem(a),i=localStorage.getItem(r);o&&localStorage.setItem("backup_".concat(s),o),n&&localStorage.setItem("backup_".concat(a),n),i&&localStorage.setItem("backup_".concat(r),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:o,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await o().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await a.j2.signOut(),o().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),o().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,s),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let s=localStorage.getItem(e);s&&new Date(parseInt(s)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},1020:(e,t,s)=>{Promise.resolve().then(s.bind(s,6311))},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>c});var a=s(3915),r=s(3004),o=s(5317),n=s(858);let i=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,r.xI)(i),l=(0,o.aU)(i);(0,n.c7)(i)},6311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5155),r=s(2115),o=s(6681),n=s(3592),i=s(4752),c=s.n(i);function l(){let{user:e,loading:t}=(0,o.Nu)(),[i,l]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),g=(null==e?void 0:e.email)==="<EMAIL>",f=async()=>{if(!g)return void c().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await c().fire({icon:"warning",title:"Fix All Users Active Days",text:"This will recalculate and update active days for all users. This may take a while. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Fix All",cancelButtonText:"Cancel"})).isConfirmed)try{l(!0);let e=await (0,n.gj)();h(e),c().fire({icon:"success",title:"Active Days Fixed!",html:'\n          <div class="text-left">\n            <p><strong>Fixed:</strong> '.concat(e.fixedCount," users</p>\n            <p><strong>Errors:</strong> ").concat(e.errorCount," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error fixing active days:",e),c().fire({icon:"error",title:"Error",text:"Failed to fix active days. Check console for details."})}finally{l(!1)}},x=async()=>{if(!g)return void c().fire({icon:"error",title:"Access Denied",text:"Only admin can perform this action."});if((await c().fire({icon:"warning",title:"Reset All Daily Video Counts",text:"This will reset today's video count to 0 for all users. Continue?",showCancelButton:!0,confirmButtonText:"Yes, Reset All",cancelButtonText:"Cancel"})).isConfirmed)try{u(!0);let{getDocs:e,collection:t}=await Promise.resolve().then(s.bind(s,5317)),{db:a}=await Promise.resolve().then(s.bind(s,6104)),{COLLECTIONS:r}=await Promise.resolve().then(s.bind(s,3592)),o=await e(t(a,r.users)),i=0,l=0;for(let e of o.docs)try{await (0,n.HY)(e.id),i++}catch(t){console.error("Error resetting daily count for user ".concat(e.id,":"),t),l++}c().fire({icon:"success",title:"Daily Counts Reset!",html:'\n          <div class="text-left">\n            <p><strong>Reset:</strong> '.concat(i," users</p>\n            <p><strong>Errors:</strong> ").concat(l," users</p>\n          </div>\n        "),timer:5e3})}catch(e){console.error("Error resetting daily counts:",e),c().fire({icon:"error",title:"Error",text:"Failed to reset daily counts. Check console for details."})}finally{u(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading..."})]})}):g?(0,a.jsx)("div",{className:"min-h-screen p-4",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold text-white mb-6",children:[(0,a.jsx)("i",{className:"fas fa-tools mr-2"}),"Fix Active Days & Daily Counts"]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,a.jsx)("i",{className:"fas fa-calendar-check mr-2"}),"Fix Active Days"]}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Recalculates and updates active days for all users based on their plan activation date and leave history."}),(0,a.jsx)("button",{onClick:f,disabled:i,className:"btn-primary ".concat(i?"opacity-50 cursor-not-allowed":""),children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Fixing Active Days..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-wrench mr-2"}),"Fix All Users Active Days"]})})]}),(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-white mb-3",children:[(0,a.jsx)("i",{className:"fas fa-redo mr-2"}),"Reset Daily Video Counts"]}),(0,a.jsx)("p",{className:"text-white/80 mb-4",children:"Resets today's video count to 0 for all users. Use this if daily counts are showing incorrect values."}),(0,a.jsx)("button",{onClick:x,disabled:d,className:"btn-secondary ".concat(d?"opacity-50 cursor-not-allowed":""),children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Resetting Daily Counts..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Reset All Daily Counts"]})})]}),m&&(0,a.jsxs)("div",{className:"bg-green-500/20 border border-green-400/30 rounded-lg p-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-green-300 mb-2",children:[(0,a.jsx)("i",{className:"fas fa-check-circle mr-2"}),"Last Operation Results"]}),(0,a.jsxs)("div",{className:"text-white",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Fixed:"})," ",m.fixedCount," users"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Errors:"})," ",m.errorCount," users"]})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("a",{href:"/admin",className:"btn-secondary",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin Dashboard"]})})]})]})})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-white mb-4",children:"Only admin can access this page."}),(0,a.jsx)("a",{href:"/admin",className:"btn-primary",children:"Back to Admin"})]})})}},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>i,wC:()=>l});var a=s(2115),r=s(3004),o=s(6104),n=s(12);function i(){let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,r.hg)(o.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=i();return(0,a.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=i(),[s,r]=(0,a.useState)(!1),[o,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||o,isAdmin:s}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,3592,8441,1684,7358],()=>t(1020)),_N_E=e.O()}]);