(()=>{var e={};e.id=3179,e.ids=[3179],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},782:(e,r,s)=>{Promise.resolve().then(s.bind(s,68163))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5969:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\daily-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,r,s)=>{"use strict";s.d(r,{db:()=>c,j2:()=>l});var t=s(67989),i=s(63385),n=s(75535),a=s(70146);let o=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(o),c=(0,n.aU)(o);(0,a.c7)(o)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40102:(e,r,s)=>{Promise.resolve().then(s.bind(s,5969))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,r,s)=>{"use strict";s.d(r,{M4:()=>o,_f:()=>a});var t=s(33784),i=s(77567);function n(e){try{Object.keys(localStorage).forEach(r=>{(r.includes(e)||r.startsWith("video_session_")||r.startsWith("watch_times_")||r.startsWith("video_refresh_")||r.startsWith("video_change_notification_")||r.startsWith("leave_")||r.includes("mytube_")||r.includes("user_"))&&localStorage.removeItem(r)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e)}catch(e){console.error("Error clearing local storage:",e)}}async function a(e,r="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e),await t.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=r}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function o(e,r="/login"){try{e&&n(e),await t.j2.signOut(),window.location.href=r}catch(e){console.error("Quick logout error:",e),window.location.href=r}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58043:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var t=s(65239),i=s(48088),n=s(88170),a=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let c={children:["",{children:["admin",{children:["daily-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5969)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/daily-active-days/page",pathname:"/admin/daily-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68163:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(60687),i=s(43210),n=s(85814),a=s.n(n),o=s(87979),l=s(3582),c=s(77567);function d(){let{user:e,loading:r,isAdmin:s}=(0,o.wC)(),[n,d]=(0,i.useState)(!1),[u,x]=(0,i.useState)(null);if(r)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})});if(!s)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})});let p=async()=>{try{d(!0);let e=await (0,l.Oe)();x(e),c.A.fire({icon:"success",title:"Daily Active Days Increment Completed!",html:`
          <div class="text-left">
            <p><strong>Incremented:</strong> ${e.incrementedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            ${e.reason?`<p><strong>Reason:</strong> ${e.reason}</p>`:""}
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running daily active days increment:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run daily active days increment. Please try again."})}finally{d(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Daily Active Days Management"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Manually trigger daily active days increment for all users"})]}),(0,t.jsxs)(a(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,t.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin"]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,t.jsx)("i",{className:"fas fa-calendar-plus mr-2 text-blue-500"}),"Daily Active Days Increment"]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"How it works:"}),(0,t.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,t.jsx)("li",{children:"• Increments active days by 1 for all users (regardless of login status)"}),(0,t.jsx)("li",{children:"• Skips users who are on approved leave today"}),(0,t.jsx)("li",{children:"• Skips increment if today is an admin leave day"}),(0,t.jsx)("li",{children:"• Only processes each user once per day"}),(0,t.jsx)("li",{children:"• Preserves manually set active days by admin"})]})]}),(0,t.jsx)("button",{onClick:p,disabled:n,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Processing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-play mr-2"}),"Run Daily Active Days Increment"]})})]}),u&&(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Last Execution Result"}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.incrementedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Incremented"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:u.skippedCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-red-600",children:u.errorCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]}),u.reason&&(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm",children:u.reason})})]})]}),(0,t.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Important Notes"}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("ul",{className:"text-yellow-800 text-sm space-y-2",children:[(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Automatic Execution:"})," This process also runs automatically when users interact with the platform (once per day)"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Manual Trigger:"})," Use this page to manually trigger the process if needed"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Safety:"})," The process is safe to run multiple times per day - it will skip users already processed"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Leave Days:"})," Active days will not increment on admin leave days or user leave days"]}),(0,t.jsxs)("li",{children:["• ",(0,t.jsx)("strong",{children:"Manual Override:"})," Users with manually set active days will still get daily increments"]})]})})]})]})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,r,s)=>{"use strict";s.d(r,{Nu:()=>a,hD:()=>n,wC:()=>o});var t=s(43210);s(63385),s(33784);var i=s(51278);function n(){let[e,r]=(0,t.useState)(null),[s,n]=(0,t.useState)(!0),a=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:a}}function a(){let{user:e,loading:r}=n();return{user:e,loading:r}}function o(){let{user:e,loading:r}=n(),[s,i]=(0,t.useState)(!1),[a,o]=(0,t.useState)(!0);return{user:e,loading:r||a,isAdmin:s}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[6204,2756,7567,8441,3582],()=>s(58043));module.exports=t})();