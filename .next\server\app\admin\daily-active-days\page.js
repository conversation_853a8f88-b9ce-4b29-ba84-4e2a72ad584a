(()=>{var e={};e.id=3179,e.ids=[3179],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},782:(e,t,r)=>{Promise.resolve().then(r.bind(r,68163))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5969:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\MY PROJECTS\\\\Node Mytube\\\\src\\\\app\\\\admin\\\\daily-active-days\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:e=>{"use strict";e.exports=require("node:http2")},33784:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>l});var s=r(67989),i=r(63385),a=r(75535),o=r(70146);let n=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),l=(0,i.xI)(n),c=(0,a.aU)(n);(0,o.c7)(n)},33873:e=>{"use strict";e.exports=require("path")},34589:e=>{"use strict";e.exports=require("node:assert")},34631:e=>{"use strict";e.exports=require("tls")},37067:e=>{"use strict";e.exports=require("node:http")},37366:e=>{"use strict";e.exports=require("dns")},37540:e=>{"use strict";e.exports=require("node:console")},40102:(e,t,r)=>{Promise.resolve().then(r.bind(r,5969))},41204:e=>{"use strict";e.exports=require("string_decoder")},41692:e=>{"use strict";e.exports=require("node:tls")},41792:e=>{"use strict";e.exports=require("node:querystring")},51278:(e,t,r)=>{"use strict";r.d(t,{G9:()=>d,M4:()=>c,_f:()=>l,g4:()=>a});var s=r(33784),i=r(77567);function a(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,i=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(`backup_timestamp_${e}`);if(!a)return!1;if(new Date(parseInt(a)).toDateString()!==t)return o(e),!1;let n=localStorage.getItem(`backup_${r}`),l=localStorage.getItem(`backup_${s}`),c=localStorage.getItem(`backup_${i}`),d=!1;if(n&&(localStorage.setItem(r,n),d=!0),l&&(localStorage.setItem(s,l),d=!0),c&&(localStorage.setItem(i,c),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),o(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function o(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),r=`video_session_${e}_${t}`,s=`watch_times_${e}_${t}`,i=`daily_watch_times_${e}_${t}`,a=localStorage.getItem(r),o=localStorage.getItem(s),n=localStorage.getItem(i);a&&localStorage.setItem(`backup_${r}`,a),o&&localStorage.setItem(`backup_${s}`,o),n&&localStorage.setItem(`backup_${i}`,n),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:a,watchTimesCount:o?JSON.parse(o).length:0,dailyWatchTimesCount:n?JSON.parse(n).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e,t="/login"){try{if((await i.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await s.j2.signOut(),i.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),i.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function c(e,t="/login",r=!0){try{e&&n(e,r),await s.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function d(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let r=localStorage.getItem(e);r&&new Date(parseInt(r)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["daily-active-days",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5969)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\MY PROJECTS\\Node Mytube\\src\\app\\admin\\daily-active-days\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/daily-active-days/page",pathname:"/admin/daily-active-days",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68163:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),a=r(85814),o=r.n(a),n=r(87979),l=r(3582),c=r(77567);function d(){let{user:e,loading:t,isAdmin:r}=(0,n.wC)(),[a,d]=(0,i.useState)(!1),[u,m]=(0,i.useState)(null),[p,x]=(0,i.useState)(!1),[g,h]=(0,i.useState)(null);if(t)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"spinner w-8 h-8 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})});if(!r)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})});let y=async()=>{try{d(!0);let e=await (0,l.Oe)();m(e),c.A.fire({icon:"success",title:"Daily Active Days Increment Completed!",html:`
          <div class="text-left">
            <p><strong>Incremented:</strong> ${e.incrementedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
            ${e.reason?`<p><strong>Reason:</strong> ${e.reason}</p>`:""}
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running daily active days increment:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run daily active days increment. Please try again."})}finally{d(!1)}},v=async()=>{try{x(!0);let e=await (0,l.GA)();h(e),c.A.fire({icon:"success",title:"Quick Video Migration Completed!",html:`
          <div class="text-left">
            <p><strong>Migrated:</strong> ${e.migratedCount} users</p>
            <p><strong>Skipped:</strong> ${e.skippedCount} users</p>
            <p><strong>Errors:</strong> ${e.errorCount} users</p>
          </div>
        `,timer:5e3,showConfirmButton:!0})}catch(e){console.error("Error running quick video migration:",e),c.A.fire({icon:"error",title:"Error",text:"Failed to run quick video migration. Please try again."})}finally{x(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Daily Active Days Management"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"Manually trigger daily active days increment for all users"})]}),(0,s.jsxs)(o(),{href:"/admin",className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Admin"]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,s.jsx)("i",{className:"fas fa-calendar-plus mr-2 text-blue-500"}),"Daily Active Days Increment"]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"How it works:"}),(0,s.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Increments active days by 1 for all users (regardless of login status)"}),(0,s.jsx)("li",{children:"• Skips users who are on approved leave today"}),(0,s.jsx)("li",{children:"• Skips increment if today is an admin leave day"}),(0,s.jsx)("li",{children:"• Only processes each user once per day"}),(0,s.jsx)("li",{children:"• Preserves manually set active days by admin"})]})]}),(0,s.jsx)("button",{onClick:y,disabled:a,className:"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-play mr-2"}),"Run Daily Active Days Increment"]})})]}),u&&(0,s.jsxs)("div",{className:"border-t pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Last Execution Result"}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.incrementedCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Users Incremented"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:u.skippedCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:u.errorCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]}),u.reason&&(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("span",{className:"inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm",children:u.reason})})]})]}),(0,s.jsxs)("div",{className:"border-t pt-6 mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Important Notes"}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("ul",{className:"text-yellow-800 text-sm space-y-2",children:[(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Automatic Execution:"})," This process also runs automatically when users interact with the platform (once per day)"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Manual Trigger:"})," Use this page to manually trigger the process if needed"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Safety:"})," The process is safe to run multiple times per day - it will skip users already processed"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Leave Days:"})," Active days will not increment on admin leave days or user leave days"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Manual Override:"})," Users with manually set active days will still get daily increments"]})]})})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:[(0,s.jsx)("i",{className:"fas fa-video mr-2 text-purple-500"}),"Quick Video Advantage Migration"]}),(0,s.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)("h3",{className:"font-medium text-purple-900 mb-2",children:"Migration Purpose:"}),(0,s.jsxs)("ul",{className:"text-purple-800 text-sm space-y-1",children:[(0,s.jsx)("li",{children:"• Converts old expiry date system to new remaining days system"}),(0,s.jsx)("li",{children:"• Calculates remaining days from current expiry dates"}),(0,s.jsx)("li",{children:"• Disables expired quick video advantages"}),(0,s.jsx)("li",{children:"• Ensures proper daily decrement functionality"}),(0,s.jsx)("li",{children:"• Safe to run multiple times (skips already migrated users)"})]})]}),(0,s.jsx)("button",{onClick:v,disabled:p,className:"bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner w-4 h-4 inline-block mr-2"}),"Migrating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-sync-alt mr-2"}),"Migrate Quick Video System"]})})]}),g&&(0,s.jsxs)("div",{className:"border-t pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Migration Result"}),(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:g.migratedCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Users Migrated"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:g.skippedCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Users Skipped"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:g.errorCount}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Errors"})]})]})})]})]})]})})}},73136:e=>{"use strict";e.exports=require("node:url")},73429:e=>{"use strict";e.exports=require("node:util/types")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87979:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>o,hD:()=>a,wC:()=>n});var s=r(43210);r(63385),r(33784);var i=r(51278);function a(){let[e,t]=(0,s.useState)(null),[r,a]=(0,s.useState)(!0),o=async()=>{try{await (0,i.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:o}}function o(){let{user:e,loading:t}=a();return{user:e,loading:t}}function n(){let{user:e,loading:t}=a(),[r,i]=(0,s.useState)(!1),[o,n]=(0,s.useState)(!0);return{user:e,loading:t||o,isAdmin:r}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6204,2756,7567,8441,3582],()=>r(58043));module.exports=s})();