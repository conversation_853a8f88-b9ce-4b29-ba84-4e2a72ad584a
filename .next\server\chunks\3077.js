"use strict";exports.id=3077,exports.ids=[3077],exports.modules={33784:(e,t,a)=>{a.d(t,{db:()=>l,j2:()=>c});var o=a(67989),r=a(63385),n=a(75535),i=a(70146);let s=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,r.xI)(s),l=(0,n.aU)(s);(0,i.c7)(s)},51278:(e,t,a)=>{a.d(t,{G9:()=>u,M4:()=>l,_f:()=>c,g4:()=>n});var o=a(33784),r=a(77567);function n(e){try{let t=new Date().toDateString(),a=`video_session_${e}_${t}`,o=`watch_times_${e}_${t}`,r=`daily_watch_times_${e}_${t}`,n=localStorage.getItem(`backup_timestamp_${e}`);if(!n)return!1;if(new Date(parseInt(n)).toDateString()!==t)return i(e),!1;let s=localStorage.getItem(`backup_${a}`),c=localStorage.getItem(`backup_${o}`),l=localStorage.getItem(`backup_${r}`),u=!1;if(s&&(localStorage.setItem(a,s),u=!0),c&&(localStorage.setItem(o,c),u=!0),l&&(localStorage.setItem(r,l),u=!0),u)return console.log("Session data restored for user:",e,{sessionCount:s,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function s(e,t=!1){try{t&&function(e){try{let t=new Date().toDateString(),a=`video_session_${e}_${t}`,o=`watch_times_${e}_${t}`,r=`daily_watch_times_${e}_${t}`,n=localStorage.getItem(a),i=localStorage.getItem(o),s=localStorage.getItem(r);n&&localStorage.setItem(`backup_${a}`,n),i&&localStorage.setItem(`backup_${o}`,i),s&&localStorage.setItem(`backup_${r}`,s),localStorage.setItem(`backup_timestamp_${e}`,Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:n,watchTimesCount:i?JSON.parse(i).length:0,dailyWatchTimesCount:s?JSON.parse(s).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function c(e,t="/login"){try{if((await r.A.fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&s(e,!1),await o.j2.signOut(),r.A.fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),r.A.fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function l(e,t="/login",a=!0){try{e&&s(e,a),await o.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let a=localStorage.getItem(e);a&&new Date(parseInt(a)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},83475:(e,t,a)=>{function o(e,t,a){if(!e||0===e.length)return void alert("No data to export");let o=a||Object.keys(e[0]),r=["Account Number","Mobile Number","Mobile","Phone","Contact","User ID","Referral Code","IFSC Code","Bank Account","Account No"],n=new Blob(["\uFEFF"+[o.join(","),...e.map(e=>o.map(t=>{let a=e[t];if(null==a)return"";let o=r.some(e=>t.toLowerCase().includes(e.toLowerCase()));if("string"==typeof a){let e=a.replace(/"/g,'""');return`"${e}"`}return a instanceof Date?`"${a.toLocaleDateString()}"`:"object"==typeof a&&null!==a&&a.toDate?`"${a.toDate().toLocaleDateString()}"`:o&&("number"==typeof a||!isNaN(Number(a)))?`"${a}"`:"number"==typeof a?a.toString():`"${String(a)}"`}).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a");if(void 0!==i.download){let e=URL.createObjectURL(n);i.setAttribute("href",e),i.setAttribute("download",`${t}_${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}}function r(e){return e.map(e=>({"User ID":e.id||"",Name:e.name||"",Email:e.email||"",Mobile:String(e.mobile||""),"Referral Code":e.referralCode||"","Referred By":e.referredBy||"Direct",Plan:e.plan||"","Plan Expiry":e.planExpiry instanceof Date?e.planExpiry.toLocaleDateString():e.planExpiry?new Date(e.planExpiry).toLocaleDateString():"","Active Days":e.activeDays||0,"Total Videos":e.totalVideos||0,"Today Videos":e.todayVideos||0,"Last Video Date":e.lastVideoDate instanceof Date?e.lastVideoDate.toLocaleDateString():e.lastVideoDate?new Date(e.lastVideoDate).toLocaleDateString():"","Video Duration (seconds)":e.videoDuration||300,"Quick Video Advantage":e.quickVideoAdvantage?"Yes":"No","Quick Video Advantage Expiry":e.quickVideoAdvantageExpiry instanceof Date?e.quickVideoAdvantageExpiry.toLocaleDateString():e.quickVideoAdvantageExpiry?new Date(e.quickVideoAdvantageExpiry).toLocaleDateString():"","Quick Video Advantage Days":e.quickVideoAdvantageDays||"","Quick Video Advantage Granted By":e.quickVideoAdvantageGrantedBy||"","Wallet Balance":e.wallet||0,"Referral Bonus Credited":e.referralBonusCredited?"Yes":"No",Status:e.status||"","Joined Date":e.joinedDate instanceof Date?e.joinedDate.toLocaleDateString():e.joinedDate?new Date(e.joinedDate).toLocaleDateString():"","Joined Time":e.joinedDate instanceof Date?e.joinedDate.toLocaleTimeString():e.joinedDate?new Date(e.joinedDate).toLocaleTimeString():""}))}function n(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","User Mobile":String(e.userMobile||""),Type:e.type||"",Amount:e.amount||0,Description:e.description||"",Status:e.status||"",Date:e.date instanceof Date?e.date.toLocaleDateString():e.date?new Date(e.date).toLocaleDateString():"",Time:e.date instanceof Date?e.date.toLocaleTimeString():e.date?new Date(e.date).toLocaleTimeString():""}))}function i(e){return e.map(e=>({"User ID":e.userId||"","User Name":e.userName||"","User Email":e.userEmail||"","Mobile Number":String(e.userMobile||""),"User Plan":e.userPlan||"","Active Days":e.userActiveDays||0,"Wallet Balance":e.walletBalance||0,"Withdrawal Amount":e.amount||0,"Account Holder Name":e.bankDetails?.accountHolderName||"","Bank Name":e.bankDetails?.bankName||"","Account Number":String(e.bankDetails?.accountNumber||""),"IFSC Code":e.bankDetails?.ifscCode||"",Status:e.status||"pending","Request Date":e.requestDate instanceof Date?e.requestDate.toLocaleDateString():e.requestDate?new Date(e.requestDate).toLocaleDateString():"","Request Time":e.requestDate instanceof Date?e.requestDate.toLocaleTimeString():e.requestDate?new Date(e.requestDate).toLocaleTimeString():"","Admin Notes":e.adminNotes||""}))}function s(e){return e.map(e=>({Title:e.title,Message:e.message,Type:e.type,Target:e.target,Status:e.status,"Created Date":e.createdAt instanceof Date?e.createdAt.toLocaleDateString():e.createdAt?new Date(e.createdAt).toLocaleDateString():"","Sent Date":e.sentAt instanceof Date?e.sentAt.toLocaleDateString():e.sentAt?new Date(e.sentAt).toLocaleDateString():""}))}a.d(t,{Bf:()=>o,Fz:()=>r,Pe:()=>s,dB:()=>i,sL:()=>n})},87979:(e,t,a)=>{a.d(t,{Nu:()=>i,hD:()=>n,wC:()=>s});var o=a(43210);a(63385),a(33784);var r=a(51278);function n(){let[e,t]=(0,o.useState)(null),[a,n]=(0,o.useState)(!0),i=async()=>{try{await (0,r.M4)(e?.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:a,signOut:i}}function i(){let{user:e,loading:t}=n();return{user:e,loading:t}}function s(){let{user:e,loading:t}=n(),[a,r]=(0,o.useState)(!1),[i,s]=(0,o.useState)(!0);return{user:e,loading:t||i,isAdmin:a}}}};