(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{12:(e,t,s)=>{"use strict";s.d(t,{G9:()=>u,M4:()=>d,_f:()=>l,g4:()=>n});var a=s(6104),o=s(4752),r=s.n(o);function n(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),o="daily_watch_times_".concat(e,"_").concat(t),r=localStorage.getItem("backup_timestamp_".concat(e));if(!r)return!1;if(new Date(parseInt(r)).toDateString()!==t)return i(e),!1;let n=localStorage.getItem("backup_".concat(s)),c=localStorage.getItem("backup_".concat(a)),l=localStorage.getItem("backup_".concat(o)),d=!1;if(n&&(localStorage.setItem(s,n),d=!0),c&&(localStorage.setItem(a,c),d=!0),l&&(localStorage.setItem(o,l),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:n,watchTimesCount:c?JSON.parse(c).length:0,dailyWatchTimesCount:l?JSON.parse(l).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function c(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),s="video_session_".concat(e,"_").concat(t),a="watch_times_".concat(e,"_").concat(t),o="daily_watch_times_".concat(e,"_").concat(t),r=localStorage.getItem(s),n=localStorage.getItem(a),i=localStorage.getItem(o);r&&localStorage.setItem("backup_".concat(s),r),n&&localStorage.setItem("backup_".concat(a),n),i&&localStorage.setItem("backup_".concat(o),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:r,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await r().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&c(e,!1),await a.j2.signOut(),r().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),r().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",s=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&c(e,s),await a.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let s=localStorage.getItem(e);s&&new Date(parseInt(s)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},283:(e,t,s)=>{Promise.resolve().then(s.bind(s,4622))},1469:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return i}});let a=s(8229),o=s(8883),r=s(3063),n=a._(s(1193));function i(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=r.Image},4622:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5155),o=s(2115),r=s(6874),n=s.n(r),i=s(6766),c=s(3004),l=s(6104),d=s(6681),u=s(4752),h=s.n(u);function m(){let{user:e,loading:t}=(0,d.hD)(),[s,r]=(0,o.useState)(""),[u,m]=(0,o.useState)(""),[f,w]=(0,o.useState)(""),[g,p]=(0,o.useState)(""),[x,b]=(0,o.useState)(!1),[v,y]=(0,o.useState)(!0),[S,_]=(0,o.useState)(!1),[j,k]=(0,o.useState)(!1),[N,I]=(0,o.useState)(!1),[P,C]=(0,o.useState)(!1);(0,o.useEffect)(()=>{e&&!t&&(window.location.href="/dashboard")},[e,t]),(0,o.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("oobCode");e?(r(e),E(e)):(y(!1),h().fire({icon:"error",title:"Invalid Reset Link",text:"This password reset link is invalid or has expired. Please request a new one.",confirmButtonText:"Go to Forgot Password"}).then(()=>{window.location.href="/forgot-password"}))},[]);let E=async e=>{try{y(!0);let t=await (0,c.RE)(l.j2,e);m(t),_(!0)}catch(t){console.error("Code verification error:",t);let e="This password reset link is invalid or has expired.";switch(t.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/user-disabled":e="This account has been disabled. Please contact support.";break;case"auth/user-not-found":e="No account found for this reset link. The account may have been deleted."}h().fire({icon:"error",title:"Invalid Reset Link",text:e,confirmButtonText:"Request New Reset Link"}).then(()=>{window.location.href="/forgot-password"})}finally{y(!1)}},T=async e=>{if(e.preventDefault(),!f.trim())return void h().fire({icon:"error",title:"Password Required",text:"Please enter a new password"});if(f.length<6)return void h().fire({icon:"error",title:"Password Too Short",text:"Password must be at least 6 characters long"});if(f!==g)return void h().fire({icon:"error",title:"Passwords Don't Match",text:"Please make sure both passwords match"});b(!0);try{await (0,c.R4)(l.j2,s,f),C(!0),h().fire({icon:"success",title:"Password Reset Successful!",text:"Your password has been updated successfully. You can now login with your new password.",confirmButtonText:"Go to Login",confirmButtonColor:"#3b82f6"}).then(()=>{window.location.href="/login"})}catch(t){console.error("Password reset error:",t);let e="An error occurred while resetting your password";switch(t.code){case"auth/expired-action-code":e="This password reset link has expired. Please request a new one.";break;case"auth/invalid-action-code":e="This password reset link is invalid. Please request a new one.";break;case"auth/weak-password":e="Password is too weak. Please choose a stronger password.";break;default:e=t.message||"Failed to reset password"}h().fire({icon:"error",title:"Reset Failed",text:e})}finally{b(!1)}};return t||v?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"spinner mb-4"}),(0,a.jsx)("p",{className:"text-white",children:v?"Verifying reset link...":"Loading..."})]})}):S?(0,a.jsx)("main",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(n(),{href:"/",className:"inline-block",children:(0,a.jsx)(i.default,{src:"/logo.png",alt:"MyTube",width:120,height:120,className:"mx-auto mb-4"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Set New Password"}),(0,a.jsxs)("p",{className:"text-white/80",children:["Enter your new password for ",(0,a.jsx)("span",{className:"font-semibold text-blue-400",children:u})]})]}),(0,a.jsxs)("form",{onSubmit:T,className:"glass-card p-8 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"newPassword",className:"block text-white font-medium mb-2",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,a.jsx)("input",{type:j?"text":"password",id:"newPassword",value:f,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Enter new password",disabled:x}),(0,a.jsx)("button",{type:"button",onClick:()=>k(!j),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,a.jsx)("i",{className:"fas ".concat(j?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("i",{className:"fas fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,a.jsx)("input",{type:N?"text":"password",id:"confirmPassword",value:g,onChange:e=>p(e.target.value),className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/40",placeholder:"Confirm new password",disabled:x}),(0,a.jsx)("button",{type:"button",onClick:()=>I(!N),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white",children:(0,a.jsx)("i",{className:"fas ".concat(N?"fa-eye-slash":"fa-eye")})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:x,className:"w-full btn-primary flex items-center justify-center",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Updating Password..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"fas fa-check mr-2"}),"Update Password"]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)(n(),{href:"/login",className:"text-white/80 hover:text-white transition-colors flex items-center justify-center",children:[(0,a.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Login"]})})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("i",{className:"fas fa-times text-red-400 text-2xl"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Invalid Reset Link"}),(0,a.jsx)("p",{className:"text-white/80 mb-6",children:"This password reset link is invalid or has expired."}),(0,a.jsx)(n(),{href:"/forgot-password",className:"btn-primary",children:"Request New Reset Link"})]})})}},6104:(e,t,s)=>{"use strict";s.d(t,{db:()=>l,j2:()=>c});var a=s(3915),o=s(3004),r=s(5317),n=s(858);let i=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),c=(0,o.xI)(i),l=(0,r.aU)(i);(0,n.c7)(i)},6681:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>c,hD:()=>i,wC:()=>l});var a=s(2115),o=s(3004),r=s(6104),n=s(12);function i(){let[e,t]=(0,a.useState)(null),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{try{let e=(0,o.hg)(r.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let c=async()=>{try{await (0,n.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:s,signOut:c}}function c(){let{user:e,loading:t}=i();return(0,a.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function l(){let{user:e,loading:t}=i(),[s,o]=(0,a.useState)(!1),[r,n]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");o(t),n(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||r,isAdmin:s}}},6766:(e,t,s)=>{"use strict";s.d(t,{default:()=>o.a});var a=s(1469),o=s.n(a)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,8441,1684,7358],()=>t(283)),_N_E=e.O()}]);