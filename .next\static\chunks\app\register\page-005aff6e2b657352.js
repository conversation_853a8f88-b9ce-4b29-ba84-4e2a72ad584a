(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2454],{12:(e,t,r)=>{"use strict";r.d(t,{G9:()=>u,M4:()=>d,_f:()=>c,g4:()=>l});var o=r(6104),a=r(4752),s=r.n(a);function l(e){try{let t=new Date().toDateString(),r="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),a="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem("backup_timestamp_".concat(e));if(!s)return!1;if(new Date(parseInt(s)).toDateString()!==t)return i(e),!1;let l=localStorage.getItem("backup_".concat(r)),n=localStorage.getItem("backup_".concat(o)),c=localStorage.getItem("backup_".concat(a)),d=!1;if(l&&(localStorage.setItem(r,l),d=!0),n&&(localStorage.setItem(o,n),d=!0),c&&(localStorage.setItem(a,c),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:l,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),i(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function i(e){try{Object.keys(localStorage).forEach(t=>{t.startsWith("backup_")&&t.includes(e)&&localStorage.removeItem(t)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{t&&function(e){try{let t=new Date().toDateString(),r="video_session_".concat(e,"_").concat(t),o="watch_times_".concat(e,"_").concat(t),a="daily_watch_times_".concat(e,"_").concat(t),s=localStorage.getItem(r),l=localStorage.getItem(o),i=localStorage.getItem(a);s&&localStorage.setItem("backup_".concat(r),s),l&&localStorage.setItem("backup_".concat(o),l),i&&localStorage.setItem("backup_".concat(a),i),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:s,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:i?JSON.parse(i).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(t=>{!t.startsWith("backup_")&&(t.includes(e)||t.startsWith("video_session_")||t.startsWith("watch_times_")||t.startsWith("video_refresh_")||t.startsWith("video_change_notification_")||t.startsWith("leave_")||t.includes("mytube_")||t.includes("user_"))&&localStorage.removeItem(t)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:t})}catch(e){console.error("Error clearing local storage:",e)}}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await s().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await o.j2.signOut(),s().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=t}),!0;return!1}catch(e){return console.error("Logout error:",e),s().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&n(e,r),await o.j2.signOut(),window.location.href=t}catch(e){console.error("Quick logout error:",e),window.location.href=t}}function u(){try{let e=Object.keys(localStorage),t=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(t)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(t){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let r=localStorage.getItem(e);r&&new Date(parseInt(r)).toDateString()!==t&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getImageProps:function(){return i}});let o=r(8229),a=r(8883),s=r(3063),l=o._(r(1193));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let n=s.Image},6104:(e,t,r)=>{"use strict";r.d(t,{db:()=>c,j2:()=>n});var o=r(3915),a=r(3004),s=r(5317),l=r(858);let i=(0,o.Dk)().length?(0,o.Sx)():(0,o.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),n=(0,a.xI)(i),c=(0,s.aU)(i);(0,l.c7)(i)},6616:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var o=r(5155),a=r(2115),s=r(6874),l=r.n(s),i=r(6766),n=r(3004),c=r(5317),d=r(6104),u=r(6681),m=r(3592),g=r(4752),h=r.n(g);function f(){let{user:e,loading:t}=(0,u.hD)(),[r,s]=(0,a.useState)({name:"",email:"",mobile:"",password:"",confirmPassword:"",referralCode:""}),[g,f]=(0,a.useState)(!1),[p,w]=(0,a.useState)(!1),[b,x]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("ref");e&&s(t=>({...t,referralCode:e}))},[]);let y=e=>{let{name:t,value:r}=e.target;s(e=>({...e,[t]:r}))},S=()=>{let{name:e,email:t,mobile:o,password:a,confirmPassword:s}=r;if(!e||!t||!o||!a||!s)throw Error("Please fill in all required fields");if(e.length<2)throw Error("Name must be at least 2 characters long");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))throw Error("Please enter a valid email address");if(!/^[6-9]\d{9}$/.test(o))throw Error("Please enter a valid 10-digit mobile number");if(a.length<6)throw Error("Password must be at least 6 characters long");if(a!==s)throw Error("Passwords do not match")},v=async e=>{e.preventDefault();try{S(),f(!0),console.log("Creating user with email and password...");let e=(await (0,n.eJ)(d.j2,r.email,r.password)).user;console.log("Firebase Auth user created successfully:",e.uid),console.log("Generating referral code...");let t=Date.now().toString().slice(-4),o=Math.random().toString(36).substring(2,4).toUpperCase(),a="MY".concat(t).concat(o);console.log("Generated referral code:",a);let s={[m.FIELD_NAMES.name]:r.name.trim(),[m.FIELD_NAMES.email]:r.email.toLowerCase(),[m.FIELD_NAMES.mobile]:r.mobile,[m.FIELD_NAMES.referralCode]:a,[m.FIELD_NAMES.referredBy]:r.referralCode||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:1,[m.FIELD_NAMES.joinedDate]:c.Dc.now(),[m.FIELD_NAMES.wallet]:0,[m.FIELD_NAMES.totalVideos]:0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:30,status:"active"};console.log("Creating user document with data:",s),console.log("User UID:",e.uid),console.log("Collection:",m.COLLECTIONS.users),console.log("Document path:","".concat(m.COLLECTIONS.users,"/").concat(e.uid)),console.log("Creating user document in Firestore...");let l=(0,c.H9)(d.db,m.COLLECTIONS.users,e.uid);console.log("Document reference created:",l.path),console.log("About to create document with data:",JSON.stringify(s,null,2));try{console.log("Attempting to create document..."),console.log("User UID:",e.uid),console.log("Document path:",l.path),console.log("Auth user email:",e.email),console.log("Auth user verified:",e.emailVerified),await (0,c.BN)(l,s),console.log("✅ User document created successfully");let t=await (0,c.x7)(l);if(t.exists())console.log("✅ Document verification successful:",t.data()),console.log("✅ Registration completed successfully - both Auth and Firestore created");else throw console.error("❌ Document was not created properly"),Error("User document was not created properly")}catch(e){throw console.error("❌ Firestore setDoc failed:",e),console.error("❌ Firestore error code:",e.code),console.error("❌ Firestore error message:",e.message),console.error("❌ Full error object:",JSON.stringify(e,null,2)),console.error("❌ CRITICAL: Firebase Auth succeeded but Firestore document creation failed"),console.error("❌ User account exists but profile is incomplete"),Error("Failed to create user profile: ".concat(e.message,". Your account was created but profile setup failed. Please contact support."))}console.log("User registered successfully. Referral bonus will be processed when upgraded to paid plan."),h().fire({icon:"success",title:"Registration Successful!",text:"Your account and profile have been created successfully. Welcome to MyTube!",timer:2e3,showConfirmButton:!1}).then(()=>{console.log("✅ Complete registration successful - redirecting to dashboard..."),window.location.href="/dashboard"})}catch(t){console.error("Registration error:",t),console.error("Error code:",t.code),console.error("Error message:",t.message),console.error("Full error object:",JSON.stringify(t,null,2));let e="An error occurred during registration";if(t.message.includes("fill in all"))e=t.message;else if(t.message.includes("Name must be"))e=t.message;else if(t.message.includes("valid email"))e=t.message;else if(t.message.includes("valid 10-digit"))e=t.message;else if(t.message.includes("Password must be"))e=t.message;else if(t.message.includes("Passwords do not match"))e=t.message;else if(t.message.includes("email address is already registered"))e=t.message;else if(t.message.includes("mobile number is already registered"))e=t.message;else switch(t.code){case"auth/email-already-in-use":e="An account with this email already exists";break;case"auth/invalid-email":e="Invalid email address";break;case"auth/weak-password":e="Password is too weak";break;default:e=t.message||"Registration failed"}h().fire({icon:"error",title:"Registration Failed",text:e})}finally{f(!1)}};return t?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,o.jsx)("div",{className:"spinner"})}):(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4 py-8",children:(0,o.jsxs)("div",{className:"glass-card w-full max-w-md p-8",children:[(0,o.jsxs)("div",{className:"text-center mb-8",children:[(0,o.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,o.jsx)(i.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:50,height:50,className:"mr-3"}),(0,o.jsx)("span",{className:"text-2xl font-bold text-white",children:"MyTube"})]}),(0,o.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Create Account"}),(0,o.jsx)("p",{className:"text-white/80",children:"Join MyTube and start earning today"})]}),(0,o.jsxs)("form",{onSubmit:v,className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"name",className:"block text-white font-medium mb-2",children:"Full Name *"}),(0,o.jsx)("input",{type:"text",id:"name",name:"name",value:r.name,onChange:y,className:"form-input",placeholder:"Enter your full name",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"block text-white font-medium mb-2",children:"Email Address *"}),(0,o.jsx)("input",{type:"email",id:"email",name:"email",value:r.email,onChange:y,className:"form-input",placeholder:"Enter your email",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"mobile",className:"block text-white font-medium mb-2",children:"Mobile Number *"}),(0,o.jsx)("input",{type:"tel",id:"mobile",name:"mobile",value:r.mobile,onChange:y,className:"form-input",placeholder:"Enter 10-digit mobile number",maxLength:10,required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"block text-white font-medium mb-2",children:"Password *"}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:p?"text":"password",id:"password",name:"password",value:r.password,onChange:y,className:"form-input pr-12",placeholder:"Enter password (min 6 characters)",required:!0}),(0,o.jsx)("button",{type:"button",onClick:()=>w(!p),className:"password-toggle-btn","aria-label":p?"Hide password":"Show password",children:(0,o.jsx)("i",{className:"fas ".concat(p?"fa-eye-slash":"fa-eye")})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"confirmPassword",className:"block text-white font-medium mb-2",children:"Confirm Password *"}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:b?"text":"password",id:"confirmPassword",name:"confirmPassword",value:r.confirmPassword,onChange:y,className:"form-input pr-12",placeholder:"Confirm your password",required:!0}),(0,o.jsx)("button",{type:"button",onClick:()=>x(!b),className:"password-toggle-btn","aria-label":b?"Hide confirm password":"Show confirm password",children:(0,o.jsx)("i",{className:"fas ".concat(b?"fa-eye-slash":"fa-eye")})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"referralCode",className:"block text-white font-medium mb-2",children:"Referral Code (Optional)"}),(0,o.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:r.referralCode,onChange:y,className:"form-input",placeholder:"Enter referral code if you have one"})]}),(0,o.jsx)("button",{type:"submit",disabled:g,className:"w-full btn-primary flex items-center justify-center mt-6",children:g?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"spinner mr-2 w-5 h-5"}),"Creating Account..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-user-plus mr-2"}),"Create Account"]})})]}),(0,o.jsx)("div",{className:"mt-6 text-center",children:(0,o.jsxs)("div",{className:"text-white/60",children:["Already have an account?"," ",(0,o.jsx)(l(),{href:"/login",className:"text-white font-semibold hover:underline",children:"Sign in here"})]})}),(0,o.jsx)("div",{className:"mt-8 text-center",children:(0,o.jsxs)(l(),{href:"/",className:"text-white/80 hover:text-white transition-colors inline-flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Home"]})})]})})}},6626:(e,t,r)=>{Promise.resolve().then(r.bind(r,6616))},6681:(e,t,r)=>{"use strict";r.d(t,{Nu:()=>n,hD:()=>i,wC:()=>c});var o=r(2115),a=r(3004),s=r(6104),l=r(12);function i(){let[e,t]=(0,o.useState)(null),[r,i]=(0,o.useState)(!0);(0,o.useEffect)(()=>{try{let e=(0,a.hg)(s.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),t(e),i(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),i(!1)}},[]);let n=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:r,signOut:n}}function n(){let{user:e,loading:t}=i();return(0,o.useEffect)(()=>{t||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(t=>(t.startsWith("video_session_")||t.startsWith("watch_times_"))&&t.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,t]),{user:e,loading:t}}function c(){let{user:e,loading:t}=i(),[r,a]=(0,o.useState)(!1),[s,l]=(0,o.useState)(!0);return(0,o.useEffect)(()=>{if(!t&&!e){window.location.href="/admin/login";return}if(e){let t=["<EMAIL>","<EMAIL>"].includes(e.email||"");a(t),l(!1),t||(window.location.href="/login")}},[e,t]),{user:e,loading:t||s,isAdmin:r}}},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var o=r(1469),a=r.n(o)}},e=>{var t=t=>e(e.s=t);e.O(0,[2992,7416,8320,5181,6874,3063,3592,8441,1684,7358],()=>t(6626)),_N_E=e.O()}]);