(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2475],{12:(e,a,t)=>{"use strict";t.d(a,{G9:()=>u,M4:()=>d,_f:()=>c,g4:()=>l});var s=t(6104),r=t(4752),i=t.n(r);function l(e){try{let a=new Date().toDateString(),t="video_session_".concat(e,"_").concat(a),s="watch_times_".concat(e,"_").concat(a),r="daily_watch_times_".concat(e,"_").concat(a),i=localStorage.getItem("backup_timestamp_".concat(e));if(!i)return!1;if(new Date(parseInt(i)).toDateString()!==a)return o(e),!1;let l=localStorage.getItem("backup_".concat(t)),n=localStorage.getItem("backup_".concat(s)),c=localStorage.getItem("backup_".concat(r)),d=!1;if(l&&(localStorage.setItem(t,l),d=!0),n&&(localStorage.setItem(s,n),d=!0),c&&(localStorage.setItem(r,c),d=!0),d)return console.log("Session data restored for user:",e,{sessionCount:l,watchTimesCount:n?JSON.parse(n).length:0,dailyWatchTimesCount:c?JSON.parse(c).length:0}),o(e),!0;return!1}catch(e){return console.error("Error restoring session data:",e),!1}}function o(e){try{Object.keys(localStorage).forEach(a=>{a.startsWith("backup_")&&a.includes(e)&&localStorage.removeItem(a)})}catch(e){console.error("Error clearing backup data:",e)}}function n(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{a&&function(e){try{let a=new Date().toDateString(),t="video_session_".concat(e,"_").concat(a),s="watch_times_".concat(e,"_").concat(a),r="daily_watch_times_".concat(e,"_").concat(a),i=localStorage.getItem(t),l=localStorage.getItem(s),o=localStorage.getItem(r);i&&localStorage.setItem("backup_".concat(t),i),l&&localStorage.setItem("backup_".concat(s),l),o&&localStorage.setItem("backup_".concat(r),o),localStorage.setItem("backup_timestamp_".concat(e),Date.now().toString()),console.log("Session data preserved for user:",e,{sessionCount:i,watchTimesCount:l?JSON.parse(l).length:0,dailyWatchTimesCount:o?JSON.parse(o).length:0})}catch(e){console.error("Error preserving session data:",e)}}(e),Object.keys(localStorage).forEach(a=>{!a.startsWith("backup_")&&(a.includes(e)||a.startsWith("video_session_")||a.startsWith("watch_times_")||a.startsWith("video_refresh_")||a.startsWith("video_change_notification_")||a.startsWith("leave_")||a.includes("mytube_")||a.includes("user_"))&&localStorage.removeItem(a)}),["currentUser","authToken","userSession","appState","videoProgress","sessionData","workSession","walletCache","transactionCache"].forEach(e=>{localStorage.removeItem(e)}),console.log("Local storage cleared for user:",e,{preserveSession:a})}catch(e){console.error("Error clearing local storage:",e)}}async function c(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login";try{if((await i().fire({title:"Logout Confirmation",text:"Are you sure you want to logout?",icon:"question",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, Logout",cancelButtonText:"Cancel"})).isConfirmed)return e&&n(e,!1),await s.j2.signOut(),i().fire({icon:"success",title:"Logged Out Successfully",text:"You have been logged out. Redirecting...",timer:2e3,showConfirmButton:!1}).then(()=>{window.location.href=a}),!0;return!1}catch(e){return console.error("Logout error:",e),i().fire({icon:"error",title:"Logout Failed",text:"There was an error logging out. Please try again."}),!1}}async function d(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/login",t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{e&&n(e,t),await s.j2.signOut(),window.location.href=a}catch(e){console.error("Quick logout error:",e),window.location.href=a}}function u(){try{let e=Object.keys(localStorage),a=new Date().toDateString();e.forEach(e=>{if((e.startsWith("video_session_")||e.startsWith("watch_times_")||e.startsWith("backup_"))&&localStorage.getItem(e))try{e.includes(a)||(localStorage.removeItem(e),console.log("Cleared expired session:",e))}catch(a){localStorage.removeItem(e)}}),e.forEach(e=>{if(e.startsWith("backup_timestamp_")){let t=localStorage.getItem(e);t&&new Date(parseInt(t)).toDateString()!==a&&(localStorage.removeItem(e),console.log("Cleared expired backup timestamp:",e))}})}catch(e){console.error("Error clearing expired sessions:",e)}}},1187:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>b});var s=t(5155),r=t(2115),i=t(6874),l=t.n(i),o=t(8999),n=t(6681),c=t(3004),d=t(5317),u=t(6104),m=t(3592);function h(e){let a=[];if((!e.name||e.name.trim().length<2)&&a.push("Name is required and must be at least 2 characters"),e.email&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||a.push("Valid email address is required"),e.mobile&&/^[6-9]\d{9}$/.test(e.mobile)||a.push("Valid 10-digit mobile number is required"),(!e.password||e.password.length<6)&&a.push("Password is required and must be at least 6 characters"),e.plan&&!["Trial","Starter","Basic","Premium","Gold","Platinum","Diamond"].includes(e.plan)&&a.push("Plan must be one of: Trial, Starter, Basic, Premium, Gold, Platinum, Diamond"),e.activeDays&&(e.activeDays<0||e.activeDays>365)&&a.push("Active days must be between 0 and 365"),e.wallet&&e.wallet<0&&a.push("Wallet balance cannot be negative"),e.totalVideos&&e.totalVideos<0&&a.push("Total videos cannot be negative"),e.referralCode&&(/^MYN\d+$/.test(e.referralCode)||a.push("Referral code must follow format MYN0001, MYN0002, etc.")),void 0!==e.quickVideoAdvantage&&"boolean"!=typeof e.quickVideoAdvantage&&a.push("Quick video advantage must be true or false"),e.quickVideoAdvantageDays&&(e.quickVideoAdvantageDays<1||e.quickVideoAdvantageDays>365)&&a.push("Quick video advantage days must be between 1 and 365"),e.quickVideoAdvantageSeconds&&(e.quickVideoAdvantageSeconds<1||e.quickVideoAdvantageSeconds>420)&&a.push("Quick video advantage seconds must be between 1 and 420 (7 minutes)"),!0!==e.quickVideoAdvantage||e.quickVideoAdvantageDays||a.push("Quick video advantage days must be provided when quick advantage is enabled"),!0===e.quickVideoAdvantage&&e.quickVideoAdvantageSeconds){let a=[1,10,30,60,120,180,240,300,360,420];a.includes(e.quickVideoAdvantageSeconds)||console.warn("Quick video advantage seconds ".concat(e.quickVideoAdvantageSeconds," is not a common duration. Valid options: ").concat(a.join(", ")))}return a}async function g(e,a){try{let t=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.FIELD_NAMES.email,"==",e));if(!(await (0,d.getDocs)(t)).empty)return console.log("Email ".concat(e," already exists in Firestore")),!0;let s=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.FIELD_NAMES.mobile,"==",a));if(!(await (0,d.getDocs)(s)).empty)return console.log("Mobile ".concat(a," already exists in Firestore")),!0;return!1}catch(e){return console.error("Error checking user existence:",e),!1}}async function p(e){try{let a=h(e);if(a.length>0)return{success:!1,error:a.join(", ")};if(await g(e.email,e.mobile))return{success:!1,error:"User with this email or mobile already exists (duplicate)"};let t=(await (0,c.eJ)(u.j2,e.email,e.password)).user,s=e.referralCode;if(s){let e=(0,d.P)((0,d.collection)(u.db,m.COLLECTIONS.users),(0,d._M)(m.FIELD_NAMES.referralCode,"==",s));if(!(await (0,d.getDocs)(e)).empty)throw Error("Referral code ".concat(s," already exists. Please use a unique referral code."))}else s=await (0,m.x4)();let r=null;if(e.quickVideoAdvantage&&e.quickVideoAdvantageDays){let a=new Date;r=d.Dc.fromDate(new Date(a.getTime()+24*e.quickVideoAdvantageDays*36e5))}let i={[m.FIELD_NAMES.name]:e.name.trim(),[m.FIELD_NAMES.email]:e.email.toLowerCase(),[m.FIELD_NAMES.mobile]:e.mobile,[m.FIELD_NAMES.referralCode]:s,[m.FIELD_NAMES.referredBy]:e.referredBy||"",[m.FIELD_NAMES.referralBonusCredited]:!1,[m.FIELD_NAMES.plan]:e.plan||"Trial",[m.FIELD_NAMES.planExpiry]:null,[m.FIELD_NAMES.activeDays]:e.activeDays||1,[m.FIELD_NAMES.joinedDate]:e.joinedDate?new Date(e.joinedDate):d.Dc.now(),[m.FIELD_NAMES.wallet]:e.wallet||0,[m.FIELD_NAMES.totalVideos]:e.totalVideos||0,[m.FIELD_NAMES.todayVideos]:0,[m.FIELD_NAMES.lastVideoDate]:null,[m.FIELD_NAMES.videoDuration]:300,status:"active",[m.FIELD_NAMES.quickVideoAdvantage]:e.quickVideoAdvantage||!1,[m.FIELD_NAMES.quickVideoAdvantageExpiry]:r,[m.FIELD_NAMES.quickVideoAdvantageDays]:e.quickVideoAdvantageDays||0,[m.FIELD_NAMES.quickVideoAdvantageSeconds]:e.quickVideoAdvantageSeconds||30,[m.FIELD_NAMES.quickVideoAdvantageGrantedBy]:e.quickVideoAdvantageGrantedBy||"",[m.FIELD_NAMES.quickVideoAdvantageGrantedAt]:e.quickVideoAdvantage?d.Dc.now():null};return await (0,d.BN)((0,d.H9)(u.db,m.COLLECTIONS.users,t.uid),i),{success:!0}}catch(a){console.error("Error creating user:",a);let e="Unknown error occurred";return"auth/email-already-in-use"===a.code?e="Email address is already in use (duplicate)":"auth/invalid-email"===a.code?e="Invalid email address":"auth/weak-password"===a.code?e="Password is too weak":a.message&&(e=a.message),{success:!1,error:e}}}async function f(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let r=(await e.text()).split("\n").filter(e=>e.trim());if(r.length<2)throw Error("CSV file must have at least a header row and one data row");let i=r[0],l=i.includes("	")?"	":",",o=i.split(l).map(e=>e.trim().replace(/"/g,"")),n=[];for(let e=1;e<r.length;e++){let a=r[e].split(l).map(e=>e.trim().replace(/"/g,"")),t={};o.forEach((e,s)=>{t[e]=a[s]||""}),t.activeDays&&(t.activeDays=parseInt(t.activeDays)||0),t.wallet&&(t.wallet=parseFloat(t.wallet)||0),t.totalVideos&&(t.totalVideos=parseInt(t.totalVideos)||0),t.quickVideoAdvantage&&(t.quickVideoAdvantage="true"===t.quickVideoAdvantage.toLowerCase()),t.quickVideoAdvantageDays&&(t.quickVideoAdvantageDays=parseInt(t.quickVideoAdvantageDays)||0),t.quickVideoAdvantageSeconds&&(t.quickVideoAdvantageSeconds=parseInt(t.quickVideoAdvantageSeconds)||30),n.push(t)}for(let e=0;e<n.length;e++){let r=n[e],i="Processing user ".concat(e+1," of ").concat(n.length,": ").concat(r.name||r.email);console.log(i),window.updateUploadProgress&&window.updateUploadProgress(i);try{let i=await p(r);if(i.success)a.success++,console.log("✅ Created user: ".concat(r.email));else{var t,s;a.failed++,(null==(t=i.error)?void 0:t.includes("already exists"))||(null==(s=i.error)?void 0:s.includes("duplicate"))?(a.duplicates++,console.log("⚠️ Skipped duplicate: ".concat(r.email))):console.log("❌ Failed to create: ".concat(r.email," - ").concat(i.error)),a.errors.push("Row ".concat(e+2,": ").concat(i.error))}}catch(t){a.failed++,console.log("❌ Error creating: ".concat(r.email," - ").concat(t.message)),a.errors.push("Row ".concat(e+2,": ").concat(t.message||"Unknown error"))}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return a}catch(e){throw console.error("Error uploading users from CSV:",e),Error("Failed to process CSV file: ".concat(e.message))}}async function x(e){let a={success:0,failed:0,errors:[],duplicates:0};try{let r=await e.text(),i=JSON.parse(r);if(!Array.isArray(i))throw Error("JSON file must contain an array of user objects");for(let e=0;e<i.length;e++){let r=i[e];try{let i=await p(r);if(i.success)a.success++;else{var t,s;a.failed++,((null==(t=i.error)?void 0:t.includes("already exists"))||(null==(s=i.error)?void 0:s.includes("duplicate")))&&a.duplicates++,a.errors.push("User ".concat(e+1," (").concat(r.email,"): ").concat(i.error))}}catch(t){a.failed++,a.errors.push("User ".concat(e+1," (").concat(r.email,"): ").concat(t.message||"Unknown error"))}e%5==0?await new Promise(e=>setTimeout(e,500)):await new Promise(e=>setTimeout(e,200))}return a}catch(e){throw console.error("Error uploading users from JSON:",e),Error("Failed to process JSON file: ".concat(e.message))}}var v=t(4752),w=t.n(v);function b(){let{user:e,loading:a,isAdmin:t}=(0,n.wC)();(0,o.useRouter)();let[i,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(null),[m,g]=(0,r.useState)(null),[p,v]=(0,r.useState)("csv"),[b,y]=(0,r.useState)([]),[j,N]=(0,r.useState)(!1),[S,k]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=e=>{if(i)return e.preventDefault(),"Upload is in progress. Are you sure you want to leave?"};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[i]);let A=async()=>{if(m)try{c(!0);let e=await m.text(),a=[];if("csv"===p){let t=e.split("\n").filter(e=>e.trim());if(t.length<2)throw Error("CSV file must have at least a header row and one data row");let s=t[0],r=s.includes("	")?"	":",",i=s.split(r).map(e=>e.trim().replace(/"/g,""));a=t.slice(1).map(e=>{let a=e.split(r).map(e=>e.trim().replace(/"/g,"")),t={};return i.forEach((e,s)=>{t[e]=a[s]||""}),t})}else if(a=JSON.parse(e),!Array.isArray(a))throw Error("JSON file must contain an array of user objects");let t=a.slice(0,5),s=[];t.forEach((e,a)=>{let t=h(e);t.length>0&&s.push("Row ".concat(a+1,": ").concat(t.join(", ")))}),y(t),N(!0),s.length>0&&w().fire({icon:"warning",title:"Validation Issues Found",html:'<div class="text-left"><p>Issues found in preview data:</p><ul>'.concat(s.map(e=>"<li>".concat(e,"</li>")).join(""),"</ul></div>"),confirmButtonText:"Continue Anyway",showCancelButton:!0,cancelButtonText:"Fix Data First"})}catch(e){console.error("Error previewing file:",e),w().fire({icon:"error",title:"Preview Failed",text:e.message||"Failed to preview file. Please check the format."})}finally{c(!1)}},E=async()=>{if(m&&(await w().fire({icon:"question",title:"Confirm User Upload",html:'\n        <div class="text-left">\n          <p><strong>Are you sure you want to upload users from this file?</strong></p>\n          <br>\n          <p>This will:</p>\n          <ul>\n            <li>Create Firebase Authentication accounts</li>\n            <li>Create user documents in Firestore</li>\n            <li>Use provided referral codes or assign new sequential ones</li>\n            <li>Set up wallet and transaction data</li>\n            <li>Apply quick video advantage if specified</li>\n            <li>Validate referral code uniqueness</li>\n          </ul>\n          <br>\n          <p class="text-red-600"><strong>Warning:</strong> This action cannot be undone!</p>\n        </div>\n      ',showCancelButton:!0,confirmButtonText:"Yes, Upload Users",cancelButtonText:"Cancel",confirmButtonColor:"#dc2626"})).isConfirmed)try{let e;c(!0),u(null),k("Starting upload..."),w().fire({title:"Uploading Users",html:'\n          <div class="text-center">\n            <div class="spinner mx-auto mb-4"></div>\n            <p id="upload-progress">Starting upload...</p>\n            <p class="text-sm text-gray-600 mt-2">Please do not close this page or navigate away.</p>\n          </div>\n        ',allowOutsideClick:!1,allowEscapeKey:!1,showConfirmButton:!1,didOpen:()=>{window.updateUploadProgress=e=>{let a=document.getElementById("upload-progress");a&&(a.textContent=e)}}}),e="csv"===p?await f(m):await x(m),w().close(),u(e),e.success>0?w().fire({icon:e.failed>0?"warning":"success",title:"Upload Complete",html:'\n            <div class="text-left">\n              <p><strong>Upload Summary:</strong></p>\n              <ul>\n                <li class="text-green-600">✓ Successfully created: '.concat(e.success," users</li>\n                ").concat(e.duplicates>0?'<li class="text-yellow-600">⚠ Skipped duplicates: '.concat(e.duplicates," users</li>"):"","\n                ").concat(e.failed>0?'<li class="text-red-600">✗ Failed: '.concat(e.failed," users</li>"):"","\n              </ul>\n              ").concat(e.errors.length>0?"<br><p><strong>Errors:</strong></p><ul>".concat(e.errors.slice(0,5).map(e=>'<li class="text-red-600">'.concat(e,"</li>")).join(""),"</ul>"):"","\n            </div>\n          "),timer:e.failed>0?void 0:5e3,showConfirmButton:e.failed>0}):w().fire({icon:"error",title:"Upload Failed",text:"No users were successfully created. Please check your data and try again."})}catch(e){console.error("Error uploading users:",e),w().fire({icon:"error",title:"Upload Failed",text:e.message||"Failed to upload users. Please try again."})}finally{c(!1)}};return a?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"spinner"})}):(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 p-4",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Upload Users"}),(0,s.jsx)("p",{className:"text-white/80",children:"Transfer existing users from old platform"})]}),i?(0,s.jsxs)("button",{disabled:!0,className:"btn-secondary opacity-50 cursor-not-allowed",title:"Upload in progress - navigation disabled",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]}):(0,s.jsxs)(l(),{href:"/admin/users",className:"btn-secondary",children:[(0,s.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Back to Users"]})]}),(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload User Data"]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Upload Format"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",value:"csv",checked:"csv"===p,onChange:e=>v(e.target.value),className:"mr-2"}),(0,s.jsx)("span",{className:"text-white",children:"CSV/TSV File"})]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",value:"json",checked:"json"===p,onChange:e=>v(e.target.value),className:"mr-2"}),(0,s.jsx)("span",{className:"text-white",children:"JSON File"})]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Sample Files"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("button",{onClick:()=>{let e=new Blob(["name,email,mobile,password,plan,activeDays,wallet,totalVideos,referredBy,referralCode,quickVideoAdvantage,quickVideoAdvantageDays,quickVideoAdvantageSeconds,quickVideoAdvantageGrantedBy\nJohn Doe,<EMAIL>,9876543210,password123,Basic,30,2000,100,MYN0001,MYN1001,true,7,10,<EMAIL>\nJane Smith,<EMAIL>,9876543211,password456,Premium,25,5000,150,MYN0002,MYN1002,false,,,,,\nMike Johnson,<EMAIL>,9876543212,password789,Starter,30,1000,50,,MYN1003,true,14,30,<EMAIL>\nSarah Wilson,<EMAIL>,9876543213,password321,Gold,20,8000,200,MYN0001,MYN1004,true,3,1,<EMAIL>"],{type:"text/csv"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download="sample-users.csv",t.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample CSV"]}),(0,s.jsxs)("button",{onClick:()=>{let e=new Blob([JSON.stringify([{name:"John Doe",email:"<EMAIL>",mobile:"9876543210",password:"password123",plan:"Basic",activeDays:30,wallet:2e3,totalVideos:100,referredBy:"MYN0001",referralCode:"MYN1001",quickVideoAdvantage:!0,quickVideoAdvantageDays:7,quickVideoAdvantageSeconds:10,quickVideoAdvantageGrantedBy:"<EMAIL>"},{name:"Jane Smith",email:"<EMAIL>",mobile:"9876543211",password:"password456",plan:"Premium",activeDays:25,wallet:5e3,totalVideos:150,referredBy:"MYN0002",referralCode:"MYN1002",quickVideoAdvantage:!1},{name:"Mike Johnson",email:"<EMAIL>",mobile:"9876543212",password:"password789",plan:"Starter",activeDays:30,wallet:1e3,totalVideos:50,referralCode:"MYN1003",quickVideoAdvantage:!0,quickVideoAdvantageDays:14,quickVideoAdvantageSeconds:30,quickVideoAdvantageGrantedBy:"<EMAIL>"},{name:"Sarah Wilson",email:"<EMAIL>",mobile:"9876543213",password:"password321",plan:"Gold",activeDays:20,wallet:8e3,totalVideos:200,referredBy:"MYN0001",referralCode:"MYN1004",quickVideoAdvantage:!0,quickVideoAdvantageDays:3,quickVideoAdvantageSeconds:1,quickVideoAdvantageGrantedBy:"<EMAIL>"}],null,2)],{type:"application/json"}),a=URL.createObjectURL(e),t=document.createElement("a");t.href=a,t.download="sample-users.json",t.click(),URL.revokeObjectURL(a)},className:"btn-secondary text-sm",children:[(0,s.jsx)("i",{className:"fas fa-download mr-2"}),"Download Sample JSON"]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-white font-medium mb-2",children:"Select File"}),(0,s.jsx)("input",{type:"file",accept:"csv"===p?".csv,.tsv,.txt":".json",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];t&&(g(t),y([]),N(!1),u(null))},className:"form-input"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("button",{onClick:A,disabled:!m||i,className:"btn-secondary",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-eye mr-2"}),"Preview Data"]})}),(0,s.jsx)("button",{onClick:E,disabled:!m||i||!j,className:"btn-primary",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"spinner mr-2 w-4 h-4"}),"Uploading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"fas fa-upload mr-2"}),"Upload Users"]})})]}),i&&(0,s.jsx)("div",{className:"bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center text-yellow-300",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle mr-2"}),(0,s.jsxs)("span",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Upload in progress!"})," Please do not close this page or navigate away until the upload is complete."]})]})})]})]}),j&&b.length>0&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-table mr-2"}),"Data Preview (First 5 Records)"]}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full text-white",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-white/20",children:[(0,s.jsx)("th",{className:"text-left p-2",children:"Name"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Email"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Mobile"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Plan"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Active Days"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Wallet"}),(0,s.jsx)("th",{className:"text-left p-2",children:"Total Videos"})]})}),(0,s.jsx)("tbody",{children:b.map((e,a)=>(0,s.jsxs)("tr",{className:"border-b border-white/10",children:[(0,s.jsx)("td",{className:"p-2",children:e.name||"N/A"}),(0,s.jsx)("td",{className:"p-2",children:e.email||"N/A"}),(0,s.jsx)("td",{className:"p-2",children:e.mobile||"N/A"}),(0,s.jsx)("td",{className:"p-2",children:e.plan||"Trial"}),(0,s.jsx)("td",{className:"p-2",children:e.activeDays||0}),(0,s.jsxs)("td",{className:"p-2",children:["₹",e.wallet||0]}),(0,s.jsx)("td",{className:"p-2",children:e.totalVideos||0})]},a))})]})})]}),d&&(0,s.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-chart-bar mr-2"}),"Upload Results"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[(0,s.jsxs)("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-green-400 text-2xl font-bold",children:d.success}),(0,s.jsx)("div",{className:"text-green-300 text-sm",children:"Successfully Created"})]}),d.duplicates>0&&(0,s.jsxs)("div",{className:"bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-yellow-400 text-2xl font-bold",children:d.duplicates}),(0,s.jsx)("div",{className:"text-yellow-300 text-sm",children:"Skipped (Duplicates)"})]}),d.failed>0&&(0,s.jsxs)("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("div",{className:"text-red-400 text-2xl font-bold",children:d.failed}),(0,s.jsx)("div",{className:"text-red-300 text-sm",children:"Failed"})]})]}),d.errors.length>0&&(0,s.jsxs)("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-red-400 font-bold mb-2",children:"Errors:"}),(0,s.jsxs)("ul",{className:"text-red-300 text-sm space-y-1",children:[d.errors.slice(0,10).map((e,a)=>(0,s.jsxs)("li",{children:["• ",e]},a)),d.errors.length>10&&(0,s.jsxs)("li",{className:"text-red-400",children:["... and ",d.errors.length-10," more errors"]})]})]})]}),(0,s.jsxs)("div",{className:"glass-card p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-bold text-white mb-4",children:[(0,s.jsx)("i",{className:"fas fa-info-circle mr-2"}),"Upload Instructions"]}),(0,s.jsxs)("div",{className:"text-white/80 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"Required Fields:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"name:"})," User's full name"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"email:"})," Valid email address (must be unique)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"mobile:"})," 10-digit mobile number (must be unique)"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"password:"})," Password for the user account (min 6 characters)"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"Optional Fields:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"plan:"})," User's plan (Trial, Starter, Basic, Premium, Gold, Platinum, Diamond) - defaults to Trial"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"activeDays:"})," Number of active days remaining - defaults to 1"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"wallet:"})," Wallet balance in rupees - defaults to 0"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"totalVideos:"})," Total videos watched - defaults to 0"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"referredBy:"})," Referral code of the person who referred this user"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"referralCode:"})," User's own referral code (MYN0001, MYN0002, etc.) - auto-generated if not provided"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"quickVideoAdvantage:"})," Whether user has quick video advantage (true/false) - defaults to false"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"quickVideoAdvantageDays:"})," Number of days for quick advantage (1-365) - only if quickVideoAdvantage is true"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"quickVideoAdvantageSeconds:"})," Video duration in seconds during advantage (1-420) - defaults to 30"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"quickVideoAdvantageGrantedBy:"})," Admin who granted the advantage - optional"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-bold text-white mb-2",children:"Important Notes:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Each user will get a sequential referral code (MYN0001, MYN0002, etc.)"}),(0,s.jsx)("li",{children:"Firebase Authentication accounts will be created automatically"}),(0,s.jsx)("li",{children:"Duplicate emails or mobile numbers will be skipped"}),(0,s.jsx)("li",{children:"Users can login immediately with their email and password"}),(0,s.jsx)("li",{children:"All wallet balances and video counts will be preserved"})]})]})]})]})]})})}},6104:(e,a,t)=>{"use strict";t.d(a,{db:()=>c,j2:()=>n});var s=t(3915),r=t(3004),i=t(5317),l=t(858);let o=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyBQw7xXezFRNvmPJlt8vmMRTdq9FrnhYmQ",authDomain:"mytube-india.firebaseapp.com",projectId:"mytube-india",storageBucket:"mytube-india.firebasestorage.app",messagingSenderId:"************",appId:"1:************:web:ebedaec6a492926af2056a",measurementId:"G-R24C6N7CWJ"}),n=(0,r.xI)(o),c=(0,i.aU)(o);(0,l.c7)(o)},6681:(e,a,t)=>{"use strict";t.d(a,{Nu:()=>n,hD:()=>o,wC:()=>c});var s=t(2115),r=t(3004),i=t(6104),l=t(12);function o(){let[e,a]=(0,s.useState)(null),[t,o]=(0,s.useState)(!0);(0,s.useEffect)(()=>{try{let e=(0,r.hg)(i.j2,e=>{console.log("Auth state changed:",e?"User logged in":"No user"),a(e),o(!1)});return()=>e()}catch(e){console.error("Error in auth state listener:",e),o(!1)}},[]);let n=async()=>{try{await (0,l.M4)(null==e?void 0:e.uid,"/")}catch(e){console.error("Error signing out:",e),window.location.href="/"}};return{user:e,loading:t,signOut:n}}function n(){let{user:e,loading:a}=o();return(0,s.useEffect)(()=>{a||e||(function(){try{let e=new Date().toDateString();return Object.keys(localStorage).some(a=>(a.startsWith("video_session_")||a.startsWith("watch_times_"))&&a.includes(e))}catch(e){return console.error("Error checking for active session:",e),!1}}()?(console.log("\uD83D\uDD04 Auto-logout detected with active session data"),window.location.href="/login?restore=true"):window.location.href="/login")},[e,a]),{user:e,loading:a}}function c(){let{user:e,loading:a}=o(),[t,r]=(0,s.useState)(!1),[i,l]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{if(!a&&!e){window.location.href="/admin/login";return}if(e){let a=["<EMAIL>","<EMAIL>"].includes(e.email||"");r(a),l(!1),a||(window.location.href="/login")}},[e,a]),{user:e,loading:a||i,isAdmin:t}}},8418:(e,a,t)=>{Promise.resolve().then(t.bind(t,1187))}},e=>{var a=a=>e(e.s=a);e.O(0,[2992,7416,8320,5181,6874,3592,8441,1684,7358],()=>a(8418)),_N_E=e.O()}]);